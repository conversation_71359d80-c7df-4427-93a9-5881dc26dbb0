import React, {useRef, useState} from 'react';
import {DefaultTheme, NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {useSelector} from 'react-redux';
import {View, StyleSheet, Image, Animated, Easing, Linking} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

import Colors from '../constants/Colors';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import ResetPasswordScreen from '../screens/auth/ResetPasswordScreen';
import Welcome from '../screens/home/<USER>';
import SplashScreen from '../screens/home/<USER>';
import NoticesScreen from '../screens/NoticesScreen';
import NoticeDetailScreen from '../screens/NoticeDetailScreen';
import ChatForumScreen from '../screens/ChatForumScreen';
import HomeScreen from '../screens/home/<USER>';
import ProfileScreen from '../screens/ProfileScreen';
import ViewUserProfile from '../screens/ViewUserProfile';
import AddPostScreen from '../screens/Post/AddPostScreen';
import AppShareScreen from '../screens/AppShareScreen';

import {RootStackParamList, PostLoginTabsParamList} from '../types';
import UpdatePostScreen from '../screens/Post/UpdatePoseScreen';
import GroupForumScreen from '../screens/GroupForumScreen';
import GroupMembersScreen from '../screens/GroupMembersScreen';
import {navigate, navigationRef} from '../navigationpoint';
import {BadgeProvider, useBadge} from '../contexts/BadgeContext';
import TabBarIcon from '../components/TabBarIcon';

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    background: Colors.background,
  },
};

export default function Navigation() {
  const isAuthenticated = useSelector(
    (state: any) => state.auth.isAuthenticated,
  );

  // Deep linking configuration
  const linking = {
    prefixes: ['sgicalumni://', 'https://sgicalumni.com'],
    config: {
      screens: {
        ResetPassword: {
          path: 'reset-password',
          parse: {
            token: (token: string) => token,
          },
        },
        Login: 'login',
        Register: 'register',
        ForgotPassword: 'forgot-password',
      },
    },
  };

  // Deep link debugging
  const onStateChange = (state: any) => {
    console.log('Navigation state changed:', state);
  };

  const onReady = async () => {
    console.log('Navigation ready');

    const url = await Linking.getInitialURL();
    if (url) {
      try {
        const parsedUrl = new URL(url);
        const path = parsedUrl.pathname;
        const token = parsedUrl.searchParams.get('token');

        console.log('Initial URL:', url);
        console.log('Path:', path);
        console.log('Token:', token);

        if (path === '/reset-password' && token) {
          navigate('ResetPassword', {token});
        }
      } catch (err) {
        console.error('Failed to parse URL:', err);
      }
    }
  };

  return (
    <BadgeProvider>
      <NavigationContainer
        ref={navigationRef}
        theme={theme}
        linking={linking}
        onStateChange={onStateChange}
        onReady={onReady}>
        <RootNavigator isAuthenticated={isAuthenticated} />
      </NavigationContainer>
    </BadgeProvider>
  );
}

const Stack = createNativeStackNavigator<RootStackParamList>();

function RootNavigator({isAuthenticated}: {isAuthenticated: boolean}) {
  return (
    <Stack.Navigator
      initialRouteName={isAuthenticated ? 'PostLogin' : 'Splashscreen'}
      screenOptions={{
        headerShown: false,
      }}>
      {isAuthenticated ? (
        <>
          <Stack.Screen name="PostLogin" component={PostLoginTabs} />
          <Stack.Screen name="AddPost" component={AddPostScreen} />
          <Stack.Screen name="UpdatePost" component={UpdatePostScreen} />
          <Stack.Screen name="ViewUserProfile" component={ViewUserProfile} />
          <Stack.Screen name="AppShare" component={AppShareScreen} />
          <Stack.Screen name="chat" component={ChatForumScreen} />
          <Stack.Screen name="GroupMembers" component={GroupMembersScreen} />
          <Stack.Screen
            name="NoticeDetail"
            component={NoticeDetailScreen}
            options={{headerShown: false}}
          />
        </>
      ) : (
        <>
          <Stack.Screen name="Splashscreen" component={SplashScreen} />
          <Stack.Screen name="Welcome" component={Welcome} />
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
          <Stack.Screen
            name="ForgotPassword"
            component={ForgotPasswordScreen}
          />
          <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
        </>
      )}
    </Stack.Navigator>
  );
}
const Tab = createBottomTabNavigator<PostLoginTabsParamList>();

function PostLoginTabs({navigation}: any) {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const {noticeBadgeCount, forumBadgeCount, clearNoticeBadge, clearForumBadge} =
    useBadge();

  // Debug: Log badge counts when they change
  React.useEffect(() => {
    console.log(
      '🔔 NAVIGATION - Notice badge count changed to:',
      noticeBadgeCount,
    );
  }, [noticeBadgeCount]);

  React.useEffect(() => {
    console.log(
      '🔔 NAVIGATION - Forum badge count changed to:',
      forumBadgeCount,
    );
  }, [forumBadgeCount]);

  const handleOpenModal = () => {
    setIsModalVisible(true);
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 7,
      useNativeDriver: true,
    }).start();
  };

  const handleCloseModal = () => {
    Animated.timing(scaleAnim, {
      toValue: 0,
      duration: 200,
      easing: Easing.ease,
      useNativeDriver: true,
    }).start(() => setIsModalVisible(false));
  };

  return (
    <>
      <Tab.Navigator
        screenOptions={({route}) => ({
          tabBarIcon: ({color, size}) => {
            const iconMap: {[key: string]: string} = {
              Home: 'home',
              Profile: 'person-circle-outline',
              Forum: 'people-outline',
              Notice: 'paper-plane-outline',
            };
            const iconName = iconMap[route.name] || 'alert-circle-outline';

            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: '#1759c4',
          tabBarInactiveTintColor: 'gray',
          headerShown: false,
          tabBarStyle: {
            height: 70,
            display: 'flex',
            top: 12,
            justifyContent: 'center',
            alignItems: 'center',
          },
        })}>
        <Tab.Screen
          name="Home"
          component={HomeScreen}
          options={{
            tabBarLabel: 'Home',
            tabBarIcon: ({focused}) => (
              <View
                style={[
                  {
                    padding: 3,
                    borderRadius: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                  focused && {backgroundColor: '#cbcaf7ff'}, // WhatsApp-style green
                ]}>
                <Ionicons
                  name="home"
                  size={25}
                  color={focused ? '#1759c4' : 'gray'}
                />
              </View>
            ),
          }}
        />

        <Tab.Screen
          name="Notice"
          component={NoticesScreen}
          options={{
            tabBarLabel: 'Notices',
            headerShown: false,
            tabBarIcon: ({focused}) => (
              <TabBarIcon
                type="notice"
                focused={focused}
                iconSource={
                  focused
                    ? require('../assets/images/icon.png')
                    : require('../assets/images/group.png')
                }
                hasNotification={noticeBadgeCount > 0}
              />
            ),
          }}
          listeners={{
            tabPress: () => {
              clearNoticeBadge();
            },
          }}
        />

        <Tab.Screen
          name="Forum"
          component={GroupForumScreen}
          options={{
            tabBarIcon: ({focused}) => (
              <TabBarIcon
                type="forum"
                focused={focused}
                iconSource={require('../assets/images/group.png')} // You can change this icon
                hasNotification={forumBadgeCount > 0}
              />
            ),
          }}
          listeners={{
            tabPress: () => {
              clearForumBadge();
            },
          }}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileScreen}
          options={{
            tabBarLabel: 'Profile',
            tabBarIcon: ({focused}) => (
              <View
                style={[
                  {
                    padding: 4,
                    borderRadius: 10,
                    alignItems: 'center',
                    justifyContent: 'center',
                  },
                  focused && {backgroundColor: '#cbcaf7ff'}, // WhatsApp-style green
                ]}>
                <Ionicons
                  name="person-outline"
                  size={25}
                  color={focused ? '#1759c4' : 'gray'}
                />
              </View>
            ),
          }}
        />
      </Tab.Navigator>

      {/* <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="none"
        onRequestClose={handleCloseModal}>
        <TouchableWithoutFeedback onPress={handleCloseModal}>
          <View style={styles.modalContainer}>
            <TouchableWithoutFeedback>
              <Animated.View
                style={[
                  styles.modalContent,
                  {
                    transform: [{ scale: scaleAnim }],
                  },
                ]}>
                <TouchableOpacity
                  onPress={() => navigation.navigate('AddPost')}>
                  <View style={styles.modalTitle}>
                
                    <Text
                      style={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      New Post
                    </Text>
                  </View>
                </TouchableOpacity>
              </Animated.View>
            </TouchableWithoutFeedback>
          </View>
        </TouchableWithoutFeedback>
      </Modal> */}
    </>
  );
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '50%',
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 99,
    alignItems: 'center',
    top: 280,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'semibold',
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
  },
  modalClose: {
    fontSize: 16,
    color: 'blue',
    marginTop: 10,
  },
});
