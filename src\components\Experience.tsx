import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import {
  addUserExperience,
  getCitiesByCountry,
  getCountryList,
  getStacksPositions,
} from '../services/authService';
import {useSelector} from 'react-redux';
import {Picker} from '@react-native-picker/picker';
import {usePermissions} from '../hooks/usePermissions';
import {ALERT_TYPE, Dialog} from './CustomAlert';
import DateTimePicker from '@react-native-community/datetimepicker';
import Font from '../constants/Font';
interface SignUpFormData {
  jobTitle: string;
  stack: string;
  company: string;
  salary: string;
  joiningDate: string;
  workingPresent: string;
  endDate: string;
  address: string;
  country: string;
  city: string;
  street: string;
}
interface ExperienceProps {
  refRBSheet: any;
  userInfo: any;
  onSave?: any;
  fetchUserDetails: any;
}

const Experience: React.FC<ExperienceProps> = ({
  refRBSheet,
  onSave,
  userInfo,
  fetchUserDetails,
}) => {
  const [formData, setFormData] = useState<SignUpFormData>({
    jobTitle: '',
    stack: '',
    company: '',
    salary: '',
    joiningDate: '',
    workingPresent: '',
    endDate: '',
    address: '',
    country: '',
    city: '',
    street: '',
  });
  const userId = useSelector((state: any) => state.auth.userId);
  const {canAddExperience} = usePermissions();
  const [stacksPositions, setStacksPositions] = useState<any>([]);

  const [showJoiningDatePicker, setShowJoiningDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [countries, setCountries] = useState<any[]>([]);
  const [filteredCountries, setFilteredCountries] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [filteredCities, setFilteredCities] = useState<any[]>([]);

  const [countrySearchText, setCountrySearchText] = useState('');
  const [citySearchText, setCitySearchText] = useState('');
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showCityModal, setShowCityModal] = useState(false);

  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${year}/${month}/${day}`;
  };
  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const countryList = await getCountryList();
        setCountries(countryList || []);
        setFilteredCountries(countryList || []);
      } catch (error) {
        console.error(error);
      }
    };

    fetchCountries();
  }, []);

  const handleCountryChange = async (countryId: string) => {
    setFormData({...formData, country: countryId, city: '', street: ''});
    try {
      const citiesData = await getCitiesByCountry(countryId);
      const cities = citiesData?.data || [];
      setCities(cities || []);
      setFilteredCities(cities || []);
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const handleCityChange = async (cityId: string) => {
    setFormData({...formData, city: cityId, street: ''});
  };

  const handleCountrySearch = (searchText: string) => {
    setCountrySearchText(searchText);
    if (searchText.trim() === '') {
      setFilteredCountries(countries);
    } else {
      const filtered = countries.filter((country: any) =>
        country.name.toLowerCase().includes(searchText.toLowerCase()),
      );
      setFilteredCountries(filtered);
    }
  };

  const handleCitySearch = (searchText: string) => {
    setCitySearchText(searchText);
    if (searchText.trim() === '') {
      setFilteredCities(cities);
    } else {
      const filtered = cities.filter((city: any) =>
        city.name.toLowerCase().includes(searchText.toLowerCase()),
      );
      setFilteredCities(filtered);
    }
  };

  const selectCountry = async (country: any) => {
    setFormData({...formData, country: country.id, city: '', street: ''});
    setShowCountryModal(false);
    setCountrySearchText('');
    setFilteredCountries(countries);

    try {
      const citiesData = await getCitiesByCountry(country.id);
      const cities = citiesData?.data || [];
      setCities(cities || []);
      setFilteredCities(cities || []);
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const selectCity = (city: any) => {
    setFormData({...formData, city: city.id, street: ''});
    setShowCityModal(false);
    setCitySearchText('');
    setFilteredCities(cities);
  };

  const handleDateChange = (
    event: any,
    selectedDate: Date | undefined,
    field: string,
  ) => {
    const currentDate = selectedDate || new Date();
    const formattedDate = formatDate(currentDate.toISOString().split('T')[0]);
    setFormData({...formData, [field]: formattedDate});
    if (field === 'joiningDate') {
      setShowJoiningDatePicker(false);
    } else if (field === 'endDate') {
      setShowEndDatePicker(false);
    }
  };

  useEffect(() => {
    const fetchStacksPositions = async () => {
      try {
        const response = await getStacksPositions();
        setStacksPositions(response?.data);
      } catch (error) {
        console.log(error);
      }
    };
    fetchStacksPositions();
  }, []);
  console.log(stacksPositions?.stack);

  const handleSignUp = async () => {
    // Check permission before adding experience
    if (!canAddExperience) {
      Dialog.show({
        type: ALERT_TYPE.WARNING,
        title: 'Permission Denied',
        textBody:
          "You don't have permission to add experience. Contact your administrator for access.",
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    const payload = {
      companyName: formData.jobTitle,
      // companyAddress: formData.address,
      companyAddress: formData.street,
      cityId: formData.city,
      positionId: formData.company,
      stackId: formData.stack,
      salaryId: formData.salary || null,
      startDate: formData.joiningDate,
      workingPresent: formData.workingPresent,
      endDate: formData.workingPresent === 'Yes' ? null : formData.endDate,
      experience: '',
    };
    try {
      const response = await addUserExperience(userId, payload);
      if (onSave) {
        onSave(formData);
      }
      if (refRBSheet?.current?.close) {
        refRBSheet.current.close();
      }
      fetchUserDetails();
    } catch (error) {
      console.error('Failed to add experience:', error);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerText}>Add Experience</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Company Name</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. Invicta Innovation"
              placeholderTextColor="#999"
              value={formData.jobTitle}
              onChangeText={text => setFormData({...formData, jobTitle: text})}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Stack</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.stack}
                onValueChange={(itemValue: any) => {
                  const selectedStack = stacksPositions?.stack?.find(
                    (stack: any) => stack.id === itemValue,
                  );
                  setFormData({...formData, stack: selectedStack?.id});
                }}
                style={{
                  color: '#000000',
                  bottom: 2,
                }}>
                <Picker.Item label="Select Stack" value="" />
                {stacksPositions?.stack?.map((stack: any, index: any) => (
                  <Picker.Item
                    key={index}
                    label={stack.name}
                    value={stack.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Position</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.company}
                onValueChange={(itemValue: any) => {
                  const selectedCompany = stacksPositions?.position?.find(
                    (company: any) => company.id === itemValue,
                  );
                  setFormData({...formData, company: selectedCompany?.id});
                }}
                style={{
                  color: '#000000',
                  bottom: 2,
                }}>
                <Picker.Item label="Select Position" value="" />
                {stacksPositions?.position?.map(
                  (company: any, index: number) => (
                    <Picker.Item
                      key={index}
                      label={company.title}
                      value={company.id}
                    />
                  ),
                )}
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Salary Range</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.salary}
                onValueChange={(itemValue: any) => {
                  const selectedSalary = stacksPositions?.salary?.find(
                    (salary: any) => salary.id === itemValue,
                  );
                  setFormData({...formData, salary: selectedSalary?.id});
                }}
                style={{
                  color: '#000000',
                  bottom: 2,
                }}>
                <Picker.Item
                  label="Select Salary"
                  value=""
                  style={{color: '#a18d8d'}}
                />
                {stacksPositions?.salary?.map((salary: any, index: number) => (
                  <Picker.Item
                    key={index}
                    label={salary.name}
                    value={salary.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Joining Date</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => setShowJoiningDatePicker(true)}>
              <Text style={{color: formData.joiningDate ? '#000' : '#999'}}>
                {formData.joiningDate || 'Select Joining Date'}
              </Text>
            </TouchableOpacity>
            {showJoiningDatePicker && (
              <DateTimePicker
                value={new Date()}
                mode="date"
                display="default"
                onChange={(event, selectedDate) =>
                  handleDateChange(event, selectedDate, 'joiningDate')
                }
              />
            )}
          </View>
          {/* Working Present */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Working Present</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.workingPresent}
                onValueChange={(itemValue: any) => {
                  setFormData({...formData, workingPresent: itemValue});
                }}
                style={{
                  color: '#000000',
                  bottom: 2,
                }}>
                <Picker.Item label="Select" value="" />
                <Picker.Item label="Yes" value="Yes" />
                <Picker.Item label="No" value="No" />
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>End Date</Text>
            <TouchableOpacity
              style={styles.input}
              disabled={formData.workingPresent === 'Yes'}
              onPress={() => setShowEndDatePicker(true)}>
              <Text style={{color: formData.endDate ? '#000' : '#999'}}>
                {formData.endDate || 'Select End Date'}
              </Text>
            </TouchableOpacity>
            {showEndDatePicker && (
              <DateTimePicker
                value={new Date()}
                mode="date"
                display="default"
                onChange={(event, selectedDate) =>
                  handleDateChange(event, selectedDate, 'endDate')
                }
              />
            )}
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Country</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={() => setShowCountryModal(true)}>
              <Text style={styles.pickerButtonText}>
                {formData.country
                  ? countries.find(c => c.id === formData.country)?.name ||
                    'Select Country'
                  : 'Select Country'}
              </Text>
              <Text style={styles.pickerArrow}>▼</Text>
            </TouchableOpacity>
          </View>

          {/* City Selection */}
          {formData.country && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>City</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => setShowCityModal(true)}>
                <Text style={styles.pickerButtonText}>
                  {formData.city
                    ? cities.find(c => c.id === formData.city)?.name ||
                      'Select City'
                    : 'Select City'}
                </Text>
                <Text style={styles.pickerArrow}>▼</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Street Selection */}
          {formData.city && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Street</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. Northern Street"
                placeholderTextColor="#999"
                value={formData.street}
                onChangeText={text => setFormData({...formData, street: text})}
              />
              {/* <Picker
                selectedValue={formData.street}
                style={styles.picker}
                onValueChange={(itemValue) => setFormData({ ...formData, street: itemValue })}
              >
                <Picker.Item label="Select Street" value="" />
                {streets.map((street, index) => (
                  <Picker.Item key={index} label={street.name} value={street.id} />
                ))}
              </Picker> */}
            </View>
          )}
        </View>
        <TouchableOpacity style={styles.signUpButton} onPress={handleSignUp}>
          <Text style={styles.signUpButtonText}>Save</Text>
        </TouchableOpacity>
      </SafeAreaView>

      {/* Country Modal */}
      <Modal
        visible={showCountryModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCountryModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity
                onPress={() => setShowCountryModal(false)}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search countries..."
              placeholderTextColor="#999"
              value={countrySearchText}
              onChangeText={handleCountrySearch}
            />
            <FlatList
              data={filteredCountries}
              keyExtractor={item => item.id.toString()}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => selectCountry(item)}>
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>

      {/* City Modal */}
      <Modal
        visible={showCityModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCityModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select City</Text>
              <TouchableOpacity
                onPress={() => setShowCityModal(false)}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search cities..."
              placeholderTextColor="#999"
              value={citySearchText}
              onChangeText={handleCitySearch}
            />
            <FlatList
              data={filteredCities}
              keyExtractor={item => item.id.toString()}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => selectCity(item)}>
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  picker: {
    height: 50,
    borderColor: '#ddd6d6',
    borderWidth: 1,
    borderRadius: 8,
  },
  searchInput: {
    height: 40,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    color: '#FFFFFF',
    marginBottom: 12,
    backgroundColor: '#002157',
  },
  backButton: {
    padding: 8,
  },
  backArrow: {
    fontSize: 24,
    color: '#FF3B30',
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    marginLeft: 8,
    textAlign: 'center',
  },
  form: {
    paddingHorizontal: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#000000',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd6d6',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    justifyContent: 'center',
    color: '#333',
  },
  signUpButton: {
    backgroundColor: '#007CBC',
    marginHorizontal: 16,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  signUpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  termsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginHorizontal: 16,
    marginTop: 16,
  },
  link: {
    color: '#FF3B30',
  },
  headerText: {
    marginTop: 12,
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
  },
  pickerButton: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd6d6',
    borderRadius: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
  pickerButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  pickerArrow: {
    fontSize: 12,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listItemText: {
    fontSize: 16,
    color: '#333',
  },
});

export default Experience;
