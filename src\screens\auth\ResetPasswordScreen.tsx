import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import {NavigationProp, RouteProp} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import AppTextInput from '../../components/AppTextInput';
import normalize from '../../types/utiles';
import Font from '../../constants/Font';
import authService from '../../services/authService';
import {ALERT_TYPE, Dialog} from '../../components/CustomAlert';

type Props = {
  navigation: NavigationProp<any>;
  route: RouteProp<any>;
};

const ResetPasswordScreen: React.FC<Props> = ({navigation, route}) => {
  const {token} = route.params as {token: string};
  const [newPassword, setNewPassword] = useState<string>('');
  const [confirmPassword, setConfirmPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [verifyingToken, setVerifyingToken] = useState<boolean>(true);
  const [tokenValid, setTokenValid] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] =
    useState<boolean>(false);

  useEffect(() => {
    verifyToken();
  }, []);

  const verifyToken = async () => {
    try {
      const data = await authService.verifyResetToken(token);

      if (data.success) {
        setTokenValid(true);
      } else {
        setTokenValid(false);
        Dialog.show({
          type: ALERT_TYPE.DANGER,
          title: 'Invalid Token',
          textBody: 'This password reset link is invalid or has expired.',
          button: 'OK',
          onPressButton: () => {
            Dialog.hide();
            navigation.navigate('Login');
          },
        });
      }
    } catch (error) {
      console.error('Token verification error:', error);
      setTokenValid(false);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Unable to verify reset token. Please try again.',
        button: 'OK',
        onPressButton: () => {
          Dialog.hide();
          navigation.navigate('Login');
        },
      });
    } finally {
      setVerifyingToken(false);
    }
  };

  const handleResetPassword = async () => {
    if (!newPassword || !confirmPassword) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please fill in all fields',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (newPassword.length < 6) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Password must be at least 6 characters long',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Passwords do not match',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    setLoading(true);

    try {
      const data = await authService.resetPassword(token, newPassword);

      if (data.success) {
        Dialog.show({
          type: ALERT_TYPE.SUCCESS,
          title: 'Success',
          textBody:
            'Your password has been reset successfully. You can now login with your new password.',
          button: 'Login',
          onPressButton: () => {
            Dialog.hide();
            navigation.navigate('Login');
          },
        });
      } else {
        Dialog.show({
          type: ALERT_TYPE.DANGER,
          title: 'Error',
          textBody:
            data.message || 'Failed to reset password. Please try again.',
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      }
    } catch (error) {
      console.error('Reset password error:', error);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Network error. Please check your connection and try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
    }
  };

  if (verifyingToken) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3795BD" />
          <Text style={styles.loadingText}>Verifying reset token...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!tokenValid) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Ionicons name="close-circle" size={80} color="#f44336" />
          <Text style={styles.errorTitle}>Invalid Token</Text>
          <Text style={styles.errorDescription}>
            This password reset link is invalid or has expired.
          </Text>
          <TouchableOpacity
            style={styles.backToLoginButton}
            onPress={() => navigation.navigate('Login')}>
            <Text style={styles.backToLoginText}>Back to Login</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#002157" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.navigate('Login')}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reset Password</Text>
        <View style={styles.headerRight} />
      </View>

      <View style={styles.content}>
        {/* Title and Description */}
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Create New Password</Text>
          <Text style={styles.description}>
            Enter your new password below. Make sure it's at least 6 characters
            long.
          </Text>
        </View>

        {/* New Password Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <Ionicons
              name="lock-closed-outline"
              size={20}
              color="#3795BD"
              style={styles.inputIcon}
            />
            <AppTextInput
              placeholder="New Password"
              value={newPassword}
              onChangeText={setNewPassword}
              secureTextEntry={!showPassword}
              style={styles.textInput}
            />
            <TouchableOpacity
              onPress={() => setShowPassword(!showPassword)}
              style={styles.eyeIcon}>
              <Ionicons
                name={showPassword ? 'eye-off-outline' : 'eye-outline'}
                size={20}
                color="#666"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Confirm Password Input */}
        <View style={styles.inputContainer}>
          <View style={styles.inputWrapper}>
            <Ionicons
              name="lock-closed-outline"
              size={20}
              color="#3795BD"
              style={styles.inputIcon}
            />
            <AppTextInput
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showConfirmPassword}
              style={styles.textInput}
            />
            <TouchableOpacity
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              style={styles.eyeIcon}>
              <Ionicons
                name={showConfirmPassword ? 'eye-off-outline' : 'eye-outline'}
                size={20}
                color="#666"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Reset Password Button */}
        <TouchableOpacity
          style={[styles.resetButton, loading && styles.disabledButton]}
          onPress={handleResetPassword}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <Text style={styles.resetButtonText}>Reset Password</Text>
          )}
        </TouchableOpacity>

        {/* Back to Login */}
        <TouchableOpacity
          style={styles.backToLoginLink}
          onPress={() => navigation.navigate('Login')}>
          <Ionicons name="arrow-back" size={16} color="#3795BD" />
          <Text style={styles.backToLoginLinkText}>Back to Login</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#002157',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 12 : 12,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: normalize(20),
    paddingTop: normalize(40),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: normalize(16),
    color: '#666',
    fontFamily: Font['poppins-regular'],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: normalize(20),
  },
  errorTitle: {
    fontSize: normalize(24),
    fontWeight: '700',
    color: '#f44336',
    marginTop: 16,
    marginBottom: 8,
    fontFamily: Font['poppins-bold'],
  },
  errorDescription: {
    fontSize: normalize(16),
    color: '#666',
    textAlign: 'center',
    marginBottom: 32,
    fontFamily: Font['poppins-regular'],
  },
  titleContainer: {
    marginBottom: normalize(40),
    alignItems: 'center',
  },
  title: {
    fontSize: normalize(28),
    fontWeight: '700',
    color: '#002157',
    marginBottom: normalize(12),
    textAlign: 'center',
    fontFamily: Font['poppins-bold'],
  },
  description: {
    fontSize: normalize(16),
    color: '#666',
    textAlign: 'center',
    lineHeight: normalize(24),
    fontFamily: Font['poppins-regular'],
  },
  inputContainer: {
    marginBottom: normalize(20),
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3795BD',
    backgroundColor: '#fff',
    borderRadius: normalize(8),
    paddingHorizontal: normalize(16),
    height: normalize(50),
  },
  inputIcon: {
    marginRight: normalize(12),
  },
  textInput: {
    flex: 1,
    color: '#000',
    fontSize: normalize(16),
    fontFamily: Font['poppins-regular'],
  },
  eyeIcon: {
    padding: normalize(4),
  },
  resetButton: {
    backgroundColor: '#3795BD',
    borderRadius: normalize(8),
    height: normalize(50),
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: normalize(20),
    marginBottom: normalize(20),
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  resetButtonText: {
    color: '#fff',
    fontSize: normalize(16),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  backToLoginButton: {
    backgroundColor: '#3795BD',
    borderRadius: normalize(8),
    height: normalize(50),
    justifyContent: 'center',
    alignItems: 'center',
  },
  backToLoginText: {
    color: '#fff',
    fontSize: normalize(16),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  backToLoginLink: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: normalize(20),
  },
  backToLoginLinkText: {
    color: '#3795BD',
    fontSize: normalize(16),
    fontWeight: '500',
    marginLeft: normalize(8),
    fontFamily: Font['poppins-regular'],
  },
});

export default ResetPasswordScreen;
