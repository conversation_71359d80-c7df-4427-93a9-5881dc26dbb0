import type {ImageRequireSource} from 'react-native';
import {ALERT_TYPE} from '../config';
import type {IConfigDialog} from '../containers/Dialog';

export const getImage = (type: IConfigDialog['type']): ImageRequireSource => {
  switch (type) {
    case ALERT_TYPE.SUCCESS:
      return require('../../../assets/Alert/success.png');
    case ALERT_TYPE.WARNING:
      return require('../../../assets/Alert/warning.png');
    case ALERT_TYPE.DANGER:
      return require('../../../assets/Alert/danger.png');
    case ALERT_TYPE.INFO:
      return require('../../../assets/Alert/info.png');
  }
};
