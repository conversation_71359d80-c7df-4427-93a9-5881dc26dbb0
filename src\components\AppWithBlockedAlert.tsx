import React from 'react';
import { StatusBar } from 'react-native';
import Navigation from '../navigation';
import { useBlockedUserAlert } from './BlockedUserAlert';

const AppWithBlockedAlert: React.FC = () => {
  const { BlockedUserAlert } = useBlockedUserAlert();

  return (
    <>
      <Navigation />
      <StatusBar backgroundColor={'#002157'} barStyle="light-content" />
      {BlockedUserAlert}
    </>
  );
};

export default AppWithBlockedAlert;
