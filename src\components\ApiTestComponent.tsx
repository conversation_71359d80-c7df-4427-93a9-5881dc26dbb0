import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import apiService from '../services/apiService';

const ApiTestComponent = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');

  const testGetRequest = async () => {
    setLoading(true);
    try {
      // Test a GET request with automatic token handling
      const data = await apiService.getJson('users/profile');
      setResult(`GET Success: ${JSON.stringify(data, null, 2)}`);
      Alert.alert('Success', 'GET request completed successfully!');
    } catch (error: any) {
      setResult(`GET Error: ${error.message}`);
      Alert.alert('Error', `GET request failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPostRequest = async () => {
    setLoading(true);
    try {
      // Test a POST request with automatic token handling
      const testData = {
        message: 'Test message from API service',
        timestamp: new Date().toISOString()
      };
      
      const data = await apiService.postJson('test/endpoint', testData);
      setResult(`POST Success: ${JSON.stringify(data, null, 2)}`);
      Alert.alert('Success', 'POST request completed successfully!');
    } catch (error: any) {
      setResult(`POST Error: ${error.message}`);
      Alert.alert('Error', `POST request failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testFileUpload = async () => {
    setLoading(true);
    try {
      // Test file upload with automatic token handling
      const formData = new FormData();
      formData.append('testFile', {
        uri: 'data:text/plain;base64,SGVsbG8gV29ybGQ=', // "Hello World" in base64
        type: 'text/plain',
        name: 'test.txt'
      } as any);
      formData.append('description', 'Test file upload');
      
      const data = await apiService.uploadFile('files/upload', formData);
      setResult(`Upload Success: ${JSON.stringify(data, null, 2)}`);
      Alert.alert('Success', 'File upload completed successfully!');
    } catch (error: any) {
      setResult(`Upload Error: ${error.message}`);
      Alert.alert('Error', `File upload failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testPublicEndpoint = async () => {
    setLoading(true);
    try {
      // Test a public endpoint without authentication
      const data = await apiService.getJson('public/info', { requiresAuth: false });
      setResult(`Public Success: ${JSON.stringify(data, null, 2)}`);
      Alert.alert('Success', 'Public request completed successfully!');
    } catch (error: any) {
      setResult(`Public Error: ${error.message}`);
      Alert.alert('Error', `Public request failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>API Service Test</Text>
      
      <TouchableOpacity 
        style={[styles.button, loading && styles.buttonDisabled]} 
        onPress={testGetRequest}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Test GET Request</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.button, loading && styles.buttonDisabled]} 
        onPress={testPostRequest}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Test POST Request</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.button, loading && styles.buttonDisabled]} 
        onPress={testFileUpload}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Test File Upload</Text>
      </TouchableOpacity>

      <TouchableOpacity 
        style={[styles.button, loading && styles.buttonDisabled]} 
        onPress={testPublicEndpoint}
        disabled={loading}
      >
        <Text style={styles.buttonText}>Test Public Endpoint</Text>
      </TouchableOpacity>

      {loading && <Text style={styles.loading}>Loading...</Text>}
      
      {result ? (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Result:</Text>
          <Text style={styles.resultText}>{result}</Text>
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 15,
    alignItems: 'center',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loading: {
    textAlign: 'center',
    fontSize: 16,
    color: '#666',
    marginVertical: 20,
  },
  resultContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'monospace',
  },
});

export default ApiTestComponent;
