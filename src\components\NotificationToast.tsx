import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Colors from '../constants/Colors';

interface NotificationToastProps {
  visible: boolean;
  message: string;
  type: 'like' | 'comment' | 'new_post';
  onPress?: () => void;
  onHide: () => void;
  duration?: number;
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  visible,
  message,
  type,
  onPress,
  onHide,
  duration = 4000,
}) => {
  const [slideAnim] = useState(new Animated.Value(-100));

  useEffect(() => {
    if (visible) {
      // Slide in
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();

      // Auto hide after duration
      const timer = setTimeout(() => {
        hideToast();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [visible, duration]);

  const hideToast = () => {
    Animated.timing(slideAnim, {
      toValue: -100,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      onHide();
    });
  };

  const getIcon = () => {
    switch (type) {
      case 'like':
        return <AntDesign name="like1" size={20} color="#fff" />;
      case 'comment':
        return <AntDesign name="message1" size={20} color="#fff" />;
      case 'new_post':
        return <AntDesign name="plus" size={20} color="#fff" />;
      default:
        return <AntDesign name="notification" size={20} color="#fff" />;
    }
  };

  const getBackgroundColor = () => {
    switch (type) {
      case 'like':
        return '#3b82f6';
      case 'comment':
        return '#10b981';
      case 'new_post':
        return '#8b5cf6';
      default:
        return Colors.primary;
    }
  };

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{translateY: slideAnim}],
          backgroundColor: getBackgroundColor(),
        },
      ]}>
      <TouchableOpacity
        style={styles.content}
        onPress={onPress}
        activeOpacity={0.8}>
        <View style={styles.iconContainer}>{getIcon()}</View>
        <Text style={styles.message} numberOfLines={2}>
          {message}
        </Text>
        <TouchableOpacity onPress={hideToast} style={styles.closeButton}>
          <AntDesign name="close" size={16} color="#fff" />
        </TouchableOpacity>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 1000,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    marginRight: 12,
  },
  message: {
    flex: 1,
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
  },
  closeButton: {
    marginLeft: 8,
    padding: 4,
  },
});

export default NotificationToast;
