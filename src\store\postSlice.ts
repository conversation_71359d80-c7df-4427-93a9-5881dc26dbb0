import {createSlice, createAsyncThunk, PayloadAction} from '@reduxjs/toolkit';
import postServices from '../services/postServices';

interface PostState {
  posts: any;
  loading: boolean;
  error: string | null;
}

// Fetch Post
export const fetchPost = createAsyncThunk<any[], void, {rejectValue: string}>(
  'post/fetchPost',
  async (_, {rejectWithValue}) => {
    try {
      const response = await postServices.getPost();
      return response;
    } catch (error: any) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch posts',
      );
    }
  },
);

const initialState: PostState = {
  posts: [],
  loading: false,
  error: null,
};

const postSlice = createSlice({
  name: 'post',
  initialState,
  reducers: {},
  extraReducers: builder => {
    builder
      // Fetch Post
      .addCase(fetchPost.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPost.fulfilled, (state, action: any) => {
        state.loading = false;
        state.posts = action.payload?.posts;
      })
      .addCase(fetchPost.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'An unexpected error occurred';
      });
  },
});

export default postSlice.reducer;
