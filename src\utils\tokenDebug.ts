import AsyncStorage from '@react-native-async-storage/async-storage';

export const debugTokens = async () => {
  try {
    console.log('=== TOKEN DEBUG INFO ===');
    
    const accessToken = await AsyncStorage.getItem('accessToken');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    
    console.log('Access Token exists:', !!accessToken);
    console.log('Refresh Token exists:', !!refreshToken);
    
    if (accessToken) {
      console.log('Access Token (first 50 chars):', accessToken.substring(0, 50) + '...');
      
      // Try to decode JWT payload (without verification)
      try {
        const payload = JSON.parse(atob(accessToken.split('.')[1]));
        console.log('Access Token Payload:', payload);
        console.log('Token expires at:', new Date(payload.exp * 1000));
        console.log('Token is expired:', Date.now() > payload.exp * 1000);
      } catch (e) {
        console.log('Could not decode access token payload');
      }
    }
    
    if (refreshToken) {
      console.log('Refresh Token (first 50 chars):', refreshToken.substring(0, 50) + '...');
      
      // Try to decode JWT payload (without verification)
      try {
        const payload = JSON.parse(atob(refreshToken.split('.')[1]));
        console.log('Refresh Token Payload:', payload);
        console.log('Refresh Token expires at:', new Date(payload.exp * 1000));
        console.log('Refresh Token is expired:', Date.now() > payload.exp * 1000);
      } catch (e) {
        console.log('Could not decode refresh token payload');
      }
    }
    
    console.log('=== END TOKEN DEBUG ===');
  } catch (error) {
    console.error('Error debugging tokens:', error);
  }
};

export const clearAllTokens = async () => {
  try {
    await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
    console.log('All tokens cleared successfully');
  } catch (error) {
    console.error('Error clearing tokens:', error);
  }
};
