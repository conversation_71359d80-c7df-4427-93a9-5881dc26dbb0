import {StyleSheet} from 'react-native';
import {TextStyles, Colors} from './fonts';

// Global styles that can be used throughout the app
export const GlobalStyles = StyleSheet.create({
  // Container styles
  container: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },
  safeArea: {
    flex: 1,
    backgroundColor: Colors.background.primary,
  },

  // Header styles
  header: {
    backgroundColor: Colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: Colors.background.secondary,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    ...TextStyles.navigationTitle,
    color: Colors.text.primary,
  },

  // Card styles
  card: {
    backgroundColor: Colors.background.primary,
    borderRadius: 8,
    padding: 16,
    marginVertical: 4,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },

  // Text styles
  h1: {
    ...TextStyles.h1,
    color: Colors.text.primary,
  },
  h2: {
    ...TextStyles.h2,
    color: Colors.text.primary,
  },
  h3: {
    ...TextStyles.h3,
    color: Colors.text.primary,
  },
  h4: {
    ...TextStyles.h4,
    color: Colors.text.primary,
  },
  h5: {
    ...TextStyles.h5,
    color: Colors.text.primary,
  },
  h6: {
    ...TextStyles.h6,
    color: Colors.text.primary,
  },

  bodyLarge: {
    ...TextStyles.bodyLarge,
    color: Colors.text.primary,
  },
  bodyMedium: {
    ...TextStyles.bodyMedium,
    color: Colors.text.primary,
  },
  bodySmall: {
    ...TextStyles.bodySmall,
    color: Colors.text.secondary,
  },

  username: {
    ...TextStyles.username,
    color: Colors.text.primary,
  },
  timestamp: {
    ...TextStyles.timestamp,
    color: Colors.text.secondary,
  },
  // postContent: {
  //   ...TextStyles.postContent,
  //   color: Colors.text.primary,
  // },
  caption: {
    ...TextStyles.caption,
    color: Colors.text.tertiary,
  },

  // Button styles
  button: {
    backgroundColor: Colors.text.link,
    borderRadius: 24,
    paddingHorizontal: 24,
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    ...TextStyles.button,
    color: Colors.text.inverse,
  },
  buttonSecondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: Colors.text.link,
    borderRadius: 24,
    paddingHorizontal: 24,
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonSecondaryText: {
    ...TextStyles.button,
    color: Colors.text.link,
  },

  // Input styles
  input: {
    ...TextStyles.bodyMedium,
    borderWidth: 1,
    borderColor: Colors.background.tertiary,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: Colors.background.primary,
    color: Colors.text.primary,
  },
  inputLabel: {
    ...TextStyles.bodySmall,
    color: Colors.text.secondary,
    marginBottom: 4,
  },

  // List styles
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.background.secondary,
  },
  listItemText: {
    ...TextStyles.bodyMedium,
    color: Colors.text.primary,
    flex: 1,
  },

  // Navigation styles
  tabBarLabel: {
    ...TextStyles.caption,
    color: Colors.text.secondary,
  },
  tabBarLabelActive: {
    ...TextStyles.caption,
    color: Colors.text.link,
  },

  // Post styles
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  postAuthorName: {
    ...TextStyles.username,
    color: Colors.text.primary,
  },
  postTimestamp: {
    ...TextStyles.timestamp,
    color: Colors.text.secondary,
    marginTop: 2,
  },
  // postContent: {
  //   ...TextStyles.postContent,
  //   color: Colors.text.primary,
  //   marginBottom: 12,
  // },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.background.secondary,
  },
  postActionText: {
    ...TextStyles.bodySmall,
    color: Colors.text.secondary,
  },

  // Comment styles
  commentText: {
    ...TextStyles.bodyMedium,
    color: Colors.text.primary,
    lineHeight: 20,
  },
  commentAuthor: {
    ...TextStyles.username,
    color: Colors.text.primary,
  },
  commentTimestamp: {
    ...TextStyles.timestamp,
    color: Colors.text.tertiary,
  },

  // Form styles
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  formLabel: {
    ...TextStyles.bodyMedium,
    color: Colors.text.primary,
    marginBottom: 4,
  },
  formInput: {
    ...TextStyles.bodyMedium,
    borderWidth: 1,
    borderColor: Colors.background.tertiary,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 10,
    backgroundColor: Colors.background.primary,
    color: Colors.text.primary,
  },
  formError: {
    ...TextStyles.caption,
    color: Colors.text.error,
    marginTop: 4,
  },

  // Utility styles
  textCenter: {
    textAlign: 'center',
  },
  textLeft: {
    textAlign: 'left',
  },
  textRight: {
    textAlign: 'right',
  },

  // Spacing utilities
  mt8: {marginTop: 8},
  mt16: {marginTop: 16},
  mb8: {marginBottom: 8},
  mb16: {marginBottom: 16},
  ml8: {marginLeft: 8},
  ml16: {marginLeft: 16},
  mr8: {marginRight: 8},
  mr16: {marginRight: 16},

  // Flex utilities
  flexRow: {flexDirection: 'row'},
  flexColumn: {flexDirection: 'column'},
  alignCenter: {alignItems: 'center'},
  justifyCenter: {justifyContent: 'center'},
  justifyBetween: {justifyContent: 'space-between'},
  flex1: {flex: 1},
});
