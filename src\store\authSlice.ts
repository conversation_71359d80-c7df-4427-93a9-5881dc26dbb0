import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';
import authService, {getUserDetails} from '../services/authService';
import permissionService from '../services/permissionService';

export const login = createAsyncThunk(
  'auth/login',
  async (
    credentials: {username: string; password: string},
    {rejectWithValue},
  ) => {
    try {
      console.log('AuthSlice: Starting login process...');
      const response = await authService.loginUser(credentials);
      console.log('AuthSlice: Login service completed successfully', response);
      console.log('AuthSlice: Response data:', {
        hasUserId: !!response.userId,
        hasAccessToken: !!response.accessToken,
        hasRefreshToken: !!response.refreshToken,
        statusCode: response.statusCode,
      });

      // Initialize permissions after successful login
      if (response.user) {
        try {
          await permissionService.initializePermissions(response.user);
          console.log('✅ Permissions initialized successfully');
        } catch (permError) {
          console.error('❌ Failed to initialize permissions:', permError);
          // Don't fail login if permissions fail
        }
      } else {
        console.log(
          '⚠️ No user data in response, trying to get user details...',
        );
        try {
          const userDetails = await getUserDetails(response.userId);
          if (userDetails) {
            await permissionService.initializePermissions(userDetails);
            console.log('✅ Permissions initialized from user details');
          }
        } catch (permError) {
          console.error(
            '❌ Failed to initialize permissions from user details:',
            permError,
          );
        }
      }

      return response;
    } catch (error: any) {
      console.error('Login error in authSlice:', error);
      console.error('Error type:', typeof error);
      console.error('Error message:', error.message);

      if (error.response?.data) {
        console.error('Error response data:', error.response.data);
        return rejectWithValue(error.response.data);
      }

      // Handle session expiration or token refresh failures
      if (error.message === 'Session expired. Please login again.') {
        return rejectWithValue({
          message: 'Your session has expired. Please login again.',
        });
      }

      // Handle other specific error messages
      if (error.message === 'No refresh token available') {
        return rejectWithValue({
          message: 'Authentication failed. Please try logging in again.',
        });
      }

      return rejectWithValue({
        message: error.message || 'An unexpected error occurred',
      });
    }
  },
);

export const fetchDetails = createAsyncThunk(
  'user/fetchUserDetails',
  async (userId: number, {rejectWithValue}) => {
    try {
      const response = await getUserDetails(userId);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error);
    }
  },
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    userDetails: null,
    token: null,
    loading: false,
    isAuthenticated: false,
    error: null,
    userId: null,
    batchId: null,
    role: null,
  },
  reducers: {
    logout(state) {
      state.token = null;
      state.userId = null;
      state.role = null;
      state.isAuthenticated = false;
      state.userDetails = null;
      state.error = null;

      // Clear permissions on logout
      permissionService.clearPermissions().catch(error => {
        console.error('Failed to clear permissions on logout:', error);
      });
    },
    clearError(state) {
      state.error = null;
    },
    sessionExpired(state) {
      state.token = null;
      state.userId = null;
      state.role = null;
      state.isAuthenticated = false;
      state.userDetails = null;
      state.error = 'Your session has expired. Please login again.';
    },
  },
  extraReducers: builder => {
    builder
      .addCase(login.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action: any) => {
        state.loading = false;
        state.token = action.payload?.accessToken;
        state.userId = action.payload?.userId;
        state.batchId = action.payload?.batchId;
        state.role = action.payload?.role;
        state.isAuthenticated = true;
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.isAuthenticated = false;
        state.token = null;
        state.userId = null;
        state.role = null;
      })
      .addCase(fetchDetails.pending, state => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDetails.fulfilled, (state, action: any) => {
        state.loading = false;
        state.userDetails = action.payload;
      })
      .addCase(fetchDetails.rejected, (state, action: any) => {
        state.loading = false;
        state.error = action.payload || 'Failed to fetch user details';
      });
  },
});

export const {logout, clearError, sessionExpired} = authSlice.actions;
export default authSlice.reducer;
