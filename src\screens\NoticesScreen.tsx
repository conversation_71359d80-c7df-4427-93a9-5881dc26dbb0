import React, {useEffect, useState, useCallback} from 'react';
import {useFocusEffect} from '@react-navigation/native';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
  SafeAreaView,
  Modal,
  TouchableOpacity,
} from 'react-native';
import {useSelector} from 'react-redux';
import {format, parseISO} from 'date-fns';
import apiClient from '../services/apiService';
import normalize from '../types/utiles';
import Colors from '../constants/Colors';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useBadge} from '../contexts/BadgeContext';

const NoticesScreen = ({navigation}: any) => {
  const [notices, setNotices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const batchId = useSelector((state: any) => state.auth.batchId);
  const {clearNoticeBadge} = useBadge();

  // Clear notice badge when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('🔔 NoticesScreen focused - clearing notice badge');
      clearNoticeBadge();
    }, [clearNoticeBadge]),
  );

  // Fetch notices from the API
  const fetchNotices = async () => {
    try {
      const noticesData = await apiClient.getJson(
        `notice/batch?batchIds=[${batchId}]`,
      );
      console.log('Notices Data:', noticesData); // Debug log

      if (Array.isArray(noticesData)) {
        setNotices(noticesData);
      } else if (noticesData && Array.isArray(noticesData.data)) {
        // Handle case where data is wrapped in a data property
        setNotices(noticesData.data);
      } else {
        console.warn('Notices data is not an array:', noticesData);
        setNotices([]);
      }
    } catch (error) {
      console.error('Error fetching notices:', error);
      setNotices([]);
    } finally {
      setLoading(false);
      setRefreshing(false); // Stop the refreshing spinner
    }
  };

  useEffect(() => {
    fetchNotices();
  }, []);

  // Refresh function
  const onRefresh = useCallback(() => {
    setRefreshing(true);
    fetchNotices(); // Refetch data on pull down
  }, []);

  const handleNoticePress = (notice: any) => {
    navigation.navigate('NoticeDetail', {
      noticeId: notice.id,
      notice: notice,
    });
  };

  const renderItem = ({item}: any) => (
    <TouchableOpacity
      onPress={() => handleNoticePress(item)}
      style={{
        marginHorizontal: normalize(5),
        marginVertical: normalize(10),
      }}>
      <View
        style={{
          flexDirection: 'column',
          width: '100%',
        }}>
        <View style={{width: '100%', flexDirection: 'column'}}>
          <View
            style={[
              {
                backgroundColor: 'white',
                borderRadius: normalize(10),
                shadowColor: 'black',
                height: normalize(240),
                elevation: 5,
                overflow: 'hidden',
              },
            ]}>
            {/* Notice Type Badge */}
            <View style={styles.typeBadgeContainer}>
              <View
                style={[
                  styles.typeBadge,
                  {
                    backgroundColor:
                      item.type === 'EVENT' ? '#1890ff' : '#52c41a',
                  },
                ]}>
                <Text style={styles.typeBadgeText}>
                  {item.type || 'NOTICE'}
                </Text>
              </View>
            </View>
            <View
              style={{
                width: '100%',
                height: item.description !== 'undefined' ? '60%' : '85%',
              }}>
              <AutoSwappingImages
                images={item.images}
                isMes={item.description !== 'undefined' ? true : false}
              />
            </View>

            {item.title && (
              <View style={{flex: 1}}>
                <View
                  style={{
                    justifyContent: 'flex-end',
                    paddingLeft: normalize(10),
                    paddingRight: normalize(10),
                    gap: normalize(10),
                    marginTop: normalize(5),
                  }}>
                  <Text
                    style={{
                      fontSize: normalize(14),
                      fontWeight: '700',
                      color: Colors.text,
                    }}>
                    {item.title}
                  </Text>
                  {item.description !== 'undefined' && (
                    <Text
                      style={{
                        fontSize: normalize(12),
                        fontWeight: '600',
                        color: Colors.text,
                      }}>
                      {item.description}
                    </Text>
                  )}

                  {/* Event Details for EVENT type notices */}
                  {item.type === 'EVENT' && (
                    <View style={styles.eventDetailsCard}>
                      {item.startDate && (
                        <View style={styles.eventDetailRow}>
                          <Ionicons
                            name="calendar-outline"
                            size={14}
                            color="#666"
                          />
                          <Text style={styles.eventDetailText}>
                            {format(parseISO(item.startDate), 'MMM dd, yyyy')}
                          </Text>
                        </View>
                      )}
                      {item.startTime && (
                        <View style={styles.eventDetailRow}>
                          <Ionicons
                            name="time-outline"
                            size={14}
                            color="#666"
                          />
                          <Text style={styles.eventDetailText}>
                            {item.startTime}
                            {item.endTime && ` - ${item.endTime}`}
                          </Text>
                        </View>
                      )}
                      {item.eventUrl && (
                        <View style={styles.eventDetailRow}>
                          <Ionicons
                            name="link-outline"
                            size={14}
                            color="#007bff"
                          />
                          <Text
                            style={[
                              styles.eventDetailText,
                              {color: '#007bff'},
                            ]}>
                            Event Link Available
                          </Text>
                        </View>
                      )}
                    </View>
                  )}
                </View>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  if (notices.length === 0) {
    return (
      <View style={styles.noDataContainer}>
        <View style={styles.content}>
          <View style={styles.illustrationContainer}>
            <Image
              source={{
                uri: 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1737005088/nodata_wd6hkz.png',
              }}
              style={styles.noticeImage}
            />
          </View>
          <Text style={styles.title}>OPPS! NO NOTICES FOUND</Text>
          <Text style={styles.description}>
            Oops! Looks like there are no notices at the moment. Please check
            back later for updates!
          </Text>
        </View>
      </View>
    );
  }

  return (
    <SafeAreaView
      style={{
        flex: 1,
        backgroundColor: Colors.background,
      }}>
      <View
        style={{
          width: '100%',
          height: 150,
          overflow: 'hidden',
          marginBottom: normalize(5),
          // borderRadius: normalize(12),
          shadowColor: '#000',
          shadowOffset: {width: 0, height: 12},
          shadowOpacity: 0.9,
          shadowRadius: 15,
          elevation: 15,
        }}>
        <Image
          // source={require('../assets/images/mainpic.png')}
          source={{
            uri: 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1741760843/mainpic_uex2ib.png',
          }}
          style={{
            width: '100%',
            height: '100%',
            borderBottomRightRadius: normalize(12),
            borderBottomLeftRadius: normalize(12),
          }}
          resizeMode="contain"
        />
      </View>

      <FlatList
        data={notices}
        renderItem={renderItem}
        keyExtractor={(item: any) => item.id.toString()}
        contentContainerStyle={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      />
    </SafeAreaView>
  );
};

const AutoSwappingImages = ({
  images,
  isMes,
}: {
  images: {imageUrl: string}[];
  isMes: boolean;
}) => {
  const {width, height} = Dimensions.get('window');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  useEffect(() => {
    if (images.length > 1 && !isPaused) {
      const interval = setInterval(() => {
        setCurrentIndex(prevIndex => (prevIndex + 1) % images.length);
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [images.length, isPaused]);

  const imageUrl =
    images[currentIndex]?.imageUrl ||
    'https://cdn-icons-png.freepik.com/512/7803/7803013.png';

  return (
    <View>
      <TouchableOpacity
        onPress={() => {
          setSelectedImage(imageUrl);
          setModalVisible(true);
        }}>
        <View>
          <Image
            source={{uri: imageUrl}}
            // source={{
            //   uri:
            //     images[currentIndex]?.imageUrl ||
            //     'https://cdn-icons-png.freepik.com/512/7803/7803013.png',
            // }}
            style={[
              styles.image,
              {
                width: width * 1,
                height: isMes ? height * 0.23 : height * 0.35,
                borderTopRightRadius: 10,
                borderTopLeftRadius: 10,
              },
            ]}
            resizeMode="cover"
          />
        </View>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <View
          style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
          }}>
          {/* Close button */}
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
            }}
            onPress={() => setModalVisible(false)}>
            <Ionicons name="close" size={28} color="#fff" />
          </TouchableOpacity>

          {/* Display the image in full screen */}
          <Image
            source={{uri: selectedImage || ''}}
            style={{width: '100%', height: '50%'}}
            resizeMode="contain"
          />
        </View>
      </Modal>
    </View>
  );
};

export default NoticesScreen;

const styles = StyleSheet.create({
  container: {
    padding: 10,
  },
  noticeCard: {
    marginBottom: 20,
    padding: 15,
    borderRadius: 8,
    elevation: 5,
  },
  imageContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  image: {
    borderRadius: 1,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginVertical: 5,
    textAlign: 'center',
    color: '#333',
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  illustrationContainer: {
    width: 96,
    height: 96,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 0,
  },
  noticeImage: {
    width: 200,
    height: 100,
    marginLeft: 10,
  },
  typeBadgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  typeBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  typeBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  eventDetailsCard: {
    marginTop: 8,
    padding: 8,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    borderLeftWidth: 3,
    borderLeftColor: '#007bff',
  },
  eventDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  eventDetailText: {
    fontSize: 11,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
});
