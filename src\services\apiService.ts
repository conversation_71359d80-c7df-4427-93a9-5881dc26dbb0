import AsyncStorage from '@react-native-async-storage/async-storage';

export const baseURL = 'http://74.235.80.66:3009/api/v1/';

interface ApiRequestOptions extends RequestInit {
  requiresAuth?: boolean;
}

class ApiService {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private async getAuthHeaders(): Promise<HeadersInit> {
    const token = await AsyncStorage.getItem('accessToken');
    const headers: HeadersInit = {
      Accept: 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  private async refreshToken(): Promise<string | null> {
    try {
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${this.baseURL}auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({refreshToken}),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();
      const newAccessToken = data.accessToken;

      await AsyncStorage.setItem('accessToken', newAccessToken);
      return newAccessToken;
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Clear tokens on refresh failure
      await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
      return null;
    }
  }

  private async makeRequest(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    const {requiresAuth = true, ...fetchOptions} = options;

    let headers = {...fetchOptions.headers};

    // Add auth headers if required
    if (requiresAuth) {
      const authHeaders = await this.getAuthHeaders();
      headers = {...headers, ...authHeaders};
    }

    const url = endpoint.startsWith('http')
      ? endpoint
      : `${this.baseURL}${endpoint}`;

    let response = await fetch(url, {
      ...fetchOptions,
      headers,
    });

    // Handle 401 unauthorized - try to refresh token
    if (response.status === 401 && requiresAuth) {
      const newToken = await this.refreshToken();

      if (newToken) {
        // Retry the request with new token
        headers['Authorization'] = `Bearer ${newToken}`;
        response = await fetch(url, {
          ...fetchOptions,
          headers,
        });
      }
    }

    return response;
  }

  // GET request
  async get(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'GET',
    });
  }

  // POST request
  async post(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'POST',
    };

    // Handle different data types
    if (data instanceof FormData) {
      // Don't set Content-Type for FormData, let browser set it with boundary
      requestOptions.body = data;
    } else if (data) {
      requestOptions.headers = {
        'Content-Type': 'application/json',
        ...requestOptions.headers,
      };
      requestOptions.body = JSON.stringify(data);
    }

    return this.makeRequest(endpoint, requestOptions);
  }

  // PUT request
  async put(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'PUT',
    };

    if (data instanceof FormData) {
      requestOptions.body = data;
    } else if (data) {
      requestOptions.headers = {
        'Content-Type': 'application/json',
        ...requestOptions.headers,
      };
      requestOptions.body = JSON.stringify(data);
    }

    return this.makeRequest(endpoint, requestOptions);
  }

  // DELETE request
  async delete(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    return this.makeRequest(endpoint, {
      ...options,
      method: 'DELETE',
    });
  }

  // PATCH request
  async patch(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {},
  ): Promise<Response> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'PATCH',
    };

    if (data instanceof FormData) {
      requestOptions.body = data;
    } else if (data) {
      requestOptions.headers = {
        'Content-Type': 'application/json',
        ...requestOptions.headers,
      };
      requestOptions.body = JSON.stringify(data);
    }

    return this.makeRequest(endpoint, requestOptions);
  }

  // Utility method for handling JSON responses
  async getJson(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<any> {
    const response = await this.get(endpoint, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Utility method for POST with JSON response
  async postJson(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {},
  ): Promise<any> {
    const response = await this.post(endpoint, data, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Utility method for PUT with JSON response
  async putJson(
    endpoint: string,
    data?: any,
    options: ApiRequestOptions = {},
  ): Promise<any> {
    const response = await this.put(endpoint, data, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Utility method for DELETE with JSON response
  async deleteJson(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<any> {
    const response = await this.delete(endpoint, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }

  // Upload file method
  async uploadFile(
    endpoint: string,
    file: FormData,
    options: ApiRequestOptions = {},
  ): Promise<any> {
    const response = await this.post(endpoint, file, options);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  }
}

// Create and export a singleton instance
const apiService = new ApiService(baseURL);
export default apiService;

// Export the class for creating custom instances if needed
export {ApiService};
