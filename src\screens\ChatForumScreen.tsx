import React, {
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
  useCallback,
} from 'react';
import {
  View,
  Text,
  TextInput,
  Button,
  FlatList,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {useSelector} from 'react-redux';
import {socket} from '../services/socket';
import {format, parseISO} from 'date-fns';
import {launchImageLibrary} from 'react-native-image-picker';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {useNavigation, useRoute} from '@react-navigation/native';
import normalize from '../types/utiles';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {
  deleteGroupChat,
  editGroupChat,
  fetchMessages,
  leaveGroupService,
  sendMessage as sendMessageAPI,
  GroupService,
} from '../services/chatservice';
import DeletedMessagePlaceholder from '../components/DeletedMessagePlaceholder';
import PermissionGuard from '../components/PermissionGuard';
import {useBadge} from '../contexts/BadgeContext';
import {useFocusEffect} from '@react-navigation/native';
import VoiceRecorder from '../components/VoiceRecorder';
import VoiceMessage from '../components/VoiceMessage';

const ChatForumScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {groupId, groupName, groupimage} = route.params as {
    groupId: string;
    groupName: string;
    groupimage: string | undefined;
  };
  const userId = useSelector((state: any) => state.auth.userId);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [images, setImages] = useState<any[]>([]);
  const flatListRef = useRef<FlatList>(null);
  const [loading, setLoading] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isBlocked, setIsBlocked] = useState(false);
  const [blockInfo, setBlockInfo] = useState<any>(null);
  const [replyingTo, setReplyingTo] = useState<any>(null);
  const [isCurrentUserBlocked, setIsCurrentUserBlocked] = useState(false);
  const [isRecordingVoice, setIsRecordingVoice] = useState(false);
  const [isSendingMessage, setIsSendingMessage] = useState(false);
  const {clearForumBadge} = useBadge();

  // Clear forum badge when entering chat
  useFocusEffect(
    useCallback(() => {
      console.log('🔔 ChatForumScreen focused - clearing forum badge');
      clearForumBadge();
    }, [clearForumBadge]),
  );

  // Check if user is blocked when screen loads
  useEffect(() => {
    const checkBlockStatus = async () => {
      try {
        const blockStatus = await GroupService.checkUserBlockStatus(
          userId,
          groupId,
        );
        console.log('🚫 Chat screen block status check:', blockStatus);

        if (blockStatus.isBlocked) {
          setIsCurrentUserBlocked(true);
          Alert.alert(
            'Access Blocked',
            blockStatus.message ||
              `You are blocked from sending messages in "${groupName}" by an admin.`,
            [
              {
                text: 'OK',
                onPress: () => navigation.goBack(),
              },
            ],
          );
        } else {
          setIsCurrentUserBlocked(false);
        }
      } catch (error) {
        console.log('🚫 Block status check failed:', error);
        // If check fails, proceed anyway
      }
    };

    if (userId && groupId) {
      checkBlockStatus();
    }
  }, [userId, groupId, groupName, navigation]);

  useEffect(() => {
    if (socket.connected) {
      onConnect();
    }
    function onConnect() {
      console.log('🔌 Connecting to socket rooms...');
      socket.emit('joinGroup', `${groupId}`);
      socket.emit('joinUserRoom', userId); // Join personal room for block notifications
      console.log(
        `🔌 Joined group room: ${groupId} and user room: user_${userId}`,
      );
    }

    function onDisconnect() {
      console.log('Socket disconnected');
    }

    // Debug: Listen to all socket events
    socket.onAny((eventName, ...args) => {
      if (eventName.includes('memberBlocked') || eventName.includes('block')) {
        console.log(
          '🚫 CHAT SCREEN: Socket received block-related event:',
          eventName,
          args,
        );
      }
    });

    socket.on('connect', onConnect);
    socket.on('newMessage', message => {
      console.log('📨 Received newMessage via WebSocket:', message.id);
      // @ts-ignore
      if (message?.isDeleted) {
        // @ts-ignore
        setMessages(prevMessages =>
          prevMessages.map((msg: any) =>
            msg.id === message.id ? {...msg, isDeleted: true} : msg,
          ),
        );
      } else {
        // @ts-ignore
        setMessages(prevMessages => {
          // Check if message already exists to prevent duplicates
          const messageExists = prevMessages.some(
            (msg: any) => msg.id === message.id,
          );
          if (messageExists) {
            console.log(
              '⚠️ Message already exists, skipping duplicate:',
              message.id,
            );
            return prevMessages;
          }
          console.log('✅ Adding new message to state:', message.id);
          return [...prevMessages, message];
        });
      }
    });

    // Listen for message deletion events from web
    socket.on('messageDeleted', deletionData => {
      console.log('Message deleted from web:', deletionData);
      // @ts-ignore
      setMessages(prevMessages =>
        prevMessages.map((msg: any) => {
          if (msg.id === deletionData.id) {
            return {
              ...msg,
              isDeleted: true,
              deletedBy: deletionData.deletedBy,
              deletedAt: deletionData.deletedAt,
            };
          }
          return msg;
        }),
      );
    });

    // Listen for bulk message deletion events from web
    socket.on('messagesBulkDeleted', bulkDeletionData => {
      console.log('Bulk messages deleted from web:', bulkDeletionData);

      // For bulk deletions, we'll remove the messages and add a single notification
      // @ts-ignore
      setMessages(prevMessages => {
        const filteredMessages = prevMessages.filter(
          (msg: any) => !bulkDeletionData.deletedMessageIds.includes(msg.id),
        );

        // Add a bulk deletion notification message
        const bulkDeletionNotification = {
          id: `bulk_delete_${Date.now()}`,
          content: '',
          isDeleted: true,
          isBulkDeletion: true,
          deletedBy: bulkDeletionData.deletedBy,
          deletedAt: bulkDeletionData.deletedAt,
          bulkType: bulkDeletionData.type,
          groupId: bulkDeletionData.groupId,
          createdAt: bulkDeletionData.deletedAt,
          userId: 0, // System message
          user: {username: 'System'},
        };

        return [...filteredMessages, bulkDeletionNotification];
      });
    });

    // Listen for real-time block notifications
    socket.on('memberBlocked', blockData => {
      console.log(
        '🚫 CHAT SCREEN: Received real-time block notification:',
        blockData,
      );
      console.log('🚫 CHAT SCREEN: Current groupId:', groupId);
      console.log(
        '🚫 CHAT SCREEN: Block notification groupId:',
        blockData.groupId,
      );
      console.log('🚫 CHAT SCREEN: Current userId:', userId);

      // Check if this block notification is for the current group
      if (blockData.groupId.toString() === groupId.toString()) {
        console.log(
          '🚫 CHAT SCREEN: Block notification matches current group - updating UI',
        );
        setIsCurrentUserBlocked(true);
        Alert.alert(
          'Access Blocked',
          blockData.message ||
            `You have been blocked from sending messages in "${groupName}" by an admin.`,
          [
            {
              text: 'OK',
              onPress: () => {
                // Optionally navigate back to forum
                navigation.goBack();
              },
            },
          ],
        );
      } else {
        console.log(
          '🚫 CHAT SCREEN: Block notification is for different group - ignoring',
        );
      }
    });

    socket.on('disconnect', onDisconnect);

    return () => {
      socket.off('connect', onConnect);
      socket.off('disconnect', onDisconnect);
      socket.off('newMessage');
      socket.off('messageDeleted');
      socket.off('messagesBulkDeleted');
      socket.off('memberBlocked');
    };
  }, []);

  useEffect(() => {
    fetchMessagesByGroupId();
    checkUserBlockStatus();
  }, []);

  const checkUserBlockStatus = async () => {
    try {
      const blockStatus = await GroupService.checkBlockStatus(userId, groupId);
      setIsBlocked(blockStatus.isBlocked);
      setBlockInfo(blockStatus);
    } catch (error) {
      console.error('Error checking block status:', error);
    }
  };

  useEffect(() => {
    flatListRef.current?.scrollToEnd({animated: true});
  }, [messages]);

  const fetchMessagesByGroupId = async () => {
    try {
      // const response = await fetch(`${baseURL}messages/group/${groupId}`);
      const response = await fetchMessages(groupId, userId);
      console.log('response', response);

      if (!response) {
        throw new Error(`HTTP error! Status: ${response}`);
      }
      const messages = await response;
      setMessages(messages);
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const handleSendMessage = () => {
    // Check if user is blocked before sending message
    if (isCurrentUserBlocked) {
      Alert.alert(
        'Access Blocked',
        `You have been blocked from sending messages in "${groupName}" by an admin.`,
        [{text: 'OK'}],
      );
      return;
    }

    if (newMessage.trim() !== '' || images.length > 0) {
      const messageData = {
        content: newMessage,
        userId: userId,
        groupId: groupId,
        images: images,
        replyToMessageId: replyingTo?.id || null,
      };
      sendMessage(messageData);
      setNewMessage('');
      setImages([]);
      setReplyingTo(null); // Clear reply after sending
    }
  };

  const sendMessage = async (messageContent: any) => {
    try {
      setIsSendingMessage(true); // Start loading
      const formData = new FormData();

      formData.append('content', messageContent.content);
      formData.append('userId', messageContent.userId);
      formData.append('groupId', messageContent.groupId);

      if (messageContent.replyToMessageId) {
        formData.append(
          'replyToMessageId',
          messageContent.replyToMessageId.toString(),
        );
      }

      if (messageContent.images && messageContent.images.length > 0) {
        messageContent.images.forEach((imageUri: string, index: number) => {
          const file = {
            uri: imageUri,
            type: 'image/jpeg',
            name: `image${index}.jpg`,
          };
          formData.append('files', file);
        });
      }

      // Handle voice message
      if (messageContent.voiceMessage) {
        console.log('Preparing voice message upload:', {
          uri: messageContent.voiceMessage.uri,
          duration: messageContent.voiceMessage.duration,
        });

        // Clean the file URI - remove extra slashes
        let cleanUri = messageContent.voiceMessage.uri;
        if (cleanUri.startsWith('file:////')) {
          cleanUri = cleanUri.replace('file:////', 'file:///');
        }

        console.log('Cleaned URI:', cleanUri);

        const voiceFile = {
          uri: cleanUri,
          type: 'audio/mp4', // Try audio/mp4 instead of video/mp4
          name: `voice_${Date.now()}.mp4`,
        };

        console.log('Voice file object:', voiceFile);

        formData.append('files', voiceFile);
        formData.append('messageType', 'voice');
        formData.append(
          'voiceDuration',
          messageContent.voiceMessage.duration.toString(),
        );

        console.log('FormData prepared for voice message');
      }

      console.log('Sending message with FormData...');

      // Use the new API service with automatic token handling
      const data = await sendMessageAPI(formData);
      const {data: messageData} = data;

      console.log('📤 Message sent successfully:', messageData);

      // Add the message to local state immediately for sender
      // @ts-ignore
      setMessages(prevMessages => {
        // Check if message already exists to prevent duplicates
        const messageExists = prevMessages.some(
          (msg: any) => msg.id === messageData.id,
        );
        if (messageExists) {
          console.log(
            '⚠️ Sender message already exists, skipping duplicate:',
            messageData.id,
          );
          return prevMessages;
        }
        console.log('✅ Adding sender message to local state:', messageData.id);
        return [...prevMessages, messageData];
      });

      // Note: We don't emit via WebSocket here because the backend already does it
      // socket.emit('sendMessage', messageData); // Backend handles this
    } catch (error) {
      console.error('Error sending message:', error);

      // More detailed error logging
      if (error && typeof error === 'object' && 'response' in error) {
        console.error('Response error:', error.response);
      } else if (error && typeof error === 'object' && 'request' in error) {
        console.error('Request error:', error.request);
      } else {
        console.error('Network error:', error);
      }

      // Show user-friendly error
      Alert.alert(
        'Send Failed',
        messageContent.voiceMessage
          ? 'Failed to send voice message. Please try again.'
          : 'Failed to send message. Please try again.',
      );
    } finally {
      setIsSendingMessage(false); // Stop loading in both success and error cases
    }
  };

  const handleVoiceRecordingComplete = async (
    audioPath: string,
    duration: number,
  ) => {
    console.log('🎤 Voice recording completed:', {
      audioPath,
      duration,
      pathType: typeof audioPath,
    });

    // Test backend connectivity first
    try {
      console.log('🔍 Testing backend connectivity...');
      const testResponse = await fetch(
        'http://192.168.1.87:3006/api/v1/auth/health',
      );
      console.log(
        '🏥 Health check response:',
        testResponse.status,
        testResponse.statusText,
      );

      if (!testResponse.ok) {
        Alert.alert(
          'Connection Error',
          'Cannot connect to server. Please check your network connection.',
        );
        setIsRecordingVoice(false);
        return;
      }
    } catch (healthError) {
      console.error('🏥 Health check failed:', healthError);
      Alert.alert(
        'Connection Error',
        'Cannot connect to server. Please check your network connection.',
      );
      setIsRecordingVoice(false);
      return;
    }

    const voiceMessageData = {
      content: '', // Voice messages don't have text content
      userId: userId,
      groupId: groupId,
      voiceMessage: {
        uri: audioPath,
        duration: duration,
      },
      replyToMessageId: replyingTo?.id || null,
    };

    sendMessage(voiceMessageData);
    setReplyingTo(null); // Clear reply after sending
    setIsRecordingVoice(false);
  };

  const handleVoiceRecordingCancel = () => {
    setIsRecordingVoice(false);
  };

  const pickImages = () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        quality: 1,
        maxWidth: 800,
        maxHeight: 800,
      },
      (response: any) => {
        console.log(response);

        if (response.didCancel) {
          console.log('User canceled image picker');
        } else if (response.errorCode) {
          console.error('ImagePicker Error:', response.errorMessage);
        } else {
          // Set the selected images URIs
          setImages(prevImages => [
            ...prevImages,
            ...response?.assets.map((asset: any) => asset.uri),
          ]);
        }
      },
    );
  };

  // Group messages by date
  const groupedMessages = messages.reduce((acc: any, message: any) => {
    const date = format(parseISO(message.createdAt), 'yyyy-MM-dd');
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(message);
    return acc;
  }, {});

  const groupedMessagesArray = Object.entries(groupedMessages);

  const handleLeaveGroup = () => {
    Alert.alert(
      'Leave Group',
      'Are you sure you want to leave this group?',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'Leave', style: 'destructive', onPress: () => leaveGroup()},
      ],
      {cancelable: true},
    );
  };

  const leaveGroup = async () => {
    setLoading(true);
    try {
      await leaveGroupService(groupId, userId);
      navigation.goBack();
    } catch (error) {
      Alert.alert('Error', 'Failed to leave the group. Please try again.');
      console.error('errer:----', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditMessage = (message: any) => {
    setNewMessage(message.content);
    setSelectedMessage(message);
    setIsEditMode(true);
  };

  const handleReplyMessage = (message: any) => {
    setReplyingTo(message);
    setSelectedMessage(null);
  };

  const handleEdit = async () => {
    if (selectedMessage && newMessage.trim() !== '') {
      const updatedMessage = {
        messageId: selectedMessage.id,
        content: newMessage,
      };

      try {
        const response = await editGroupChat(updatedMessage);

        //@ts-ignore
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            //@ts-ignore
            msg.id === selectedMessage.id
              ? //@ts-ignore
                {...msg, content: newMessage, isEdited: true}
              : msg,
          ),
        );
        setSelectedMessage(null);
        setNewMessage('');
        setIsEditMode(false);
      } catch (error) {
        console.error('Error editing message:', error);
        Alert.alert('Error', 'Failed to edit message.');
      }
    }
  };

  const handleDeleteMessage = (message: any) => {
    Alert.alert('Delete Message', 'Are you sure you want to delete?', [
      {text: 'Cancel', style: 'cancel'},
      {
        text: 'Delete',
        style: 'destructive',
        onPress: async () => {
          try {
            await deleteGroupChat(message.id);
            // setMessages(prevMessages => prevMessages.filter(msg =>
            //   //@ts-ignore
            //   msg.id !== message.id));
            setSelectedMessage(null);
          } catch (error) {
            Alert.alert('Error', 'Failed to delete message.');
          }
        },
      },
    ]);
  };

  const handleMessageInfo = (message: any) => {
    Alert.alert(
      'Message Info',
      `Sent by: ${message.user.username}\nTime: ${format(
        parseISO(message.createdAt),
        'hh:mm a',
      )}`,
    );
  };

  const isMessageEditable = (createdAt: string) => {
    const messageDate = new Date(createdAt);
    const currentDate = new Date();
    const diffInMinutes =
      (currentDate.getTime() - messageDate.getTime()) / (1000 * 60);
    return diffInMinutes < 60;
  };

  return (
    <PermissionGuard permission="ENTER_CHAT_FORUM" showMessage={true}>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            backgroundColor: '#003580', // Primary blue header
            height: normalize(56),
            paddingHorizontal: normalize(12),
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 2},
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 4,
          }}>
          {selectedMessage ? (
            <>
              <TouchableOpacity onPress={() => setSelectedMessage(null)}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{marginRight: normalize(10)}}
                />
              </TouchableOpacity>

              {selectedMessage.userId === userId ? (
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <TouchableOpacity
                    onPress={() => handleReplyMessage(selectedMessage)}>
                    <Ionicons
                      name="arrow-undo-outline"
                      size={24}
                      color="white"
                      style={{marginRight: 15}}
                    />
                  </TouchableOpacity>
                  {isMessageEditable(selectedMessage.createdAt) && (
                    <TouchableOpacity
                      onPress={() => handleEditMessage(selectedMessage)}>
                      <Ionicons
                        name="create-outline"
                        size={24}
                        color="white"
                        style={{marginRight: 15}}
                      />
                    </TouchableOpacity>
                  )}
                  <TouchableOpacity
                    onPress={() => handleDeleteMessage(selectedMessage)}>
                    <Ionicons
                      name="trash-outline"
                      size={24}
                      color="white"
                      style={{marginRight: 15}}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleMessageInfo(selectedMessage)}>
                    <Ionicons
                      name="information-circle-outline"
                      size={24}
                      color="white"
                    />
                  </TouchableOpacity>
                </View>
              ) : (
                <View style={{flexDirection: 'row', alignItems: 'center'}}>
                  <TouchableOpacity
                    onPress={() => handleReplyMessage(selectedMessage)}>
                    <Ionicons
                      name="arrow-undo-outline"
                      size={24}
                      color="white"
                      style={{marginRight: 15}}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    onPress={() => handleMessageInfo(selectedMessage)}>
                    <Ionicons
                      name="information-circle-outline"
                      size={24}
                      color="white"
                    />
                  </TouchableOpacity>
                </View>
              )}
            </>
          ) : (
            <>
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Ionicons
                  name="arrow-back"
                  size={24}
                  color="white"
                  style={{marginRight: normalize(10)}}
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  flex: 1,
                  marginLeft: normalize(10),
                }}
                onPress={() =>
                  navigation.navigate('GroupMembers', {
                    groupId: groupId,
                    groupName: groupName,
                    groupImage: groupimage,
                  })
                }>
                {groupimage && (
                  <Image
                    source={{uri: groupimage}}
                    style={{
                      width: 35,
                      height: 35,
                      borderRadius: 17.5,
                      marginRight: 10,
                      backgroundColor: '#ccc',
                    }}
                    resizeMode="stretch"
                  />
                )}
                <View>
                  <Text
                    style={{
                      fontSize: normalize(20),
                      fontWeight: 'bold',
                      color: 'white',
                    }}>
                    {groupName}
                  </Text>
                  <Text
                    style={{
                      fontSize: normalize(12),
                      color: '#e0e0e0',
                    }}>
                    Tap for group info
                  </Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={handleLeaveGroup} disabled={loading}>
                <AntDesign
                  name="logout"
                  size={24}
                  color="#fff"
                  style={{marginRight: normalize(5)}}
                />
              </TouchableOpacity>
            </>
          )}
        </View>

        <View style={styles.chatContainer}>
          <FlatList
            ref={flatListRef}
            data={groupedMessagesArray}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({item}) => {
              const [date, messages] = item;

              return (
                <View>
                  {/* Date Header */}
                  <Text style={styles.dateHeader}>
                    {format(parseISO(date), 'EEEE, MMMM d, yyyy')}
                  </Text>

                  {/* Messages for the date */}
                  {messages.map((message: any, index: number) => {
                    const isCurrentUser =
                      message.userId.toString() === userId.toString();
                    const isSelected = selectedMessage?.id === message.id;

                    // Show deleted message placeholder if message is deleted
                    if (message.isDeleted) {
                      return (
                        <DeletedMessagePlaceholder
                          key={index}
                          deletedBy={message.deletedBy}
                          deletedAt={message.deletedAt}
                          type={message.isBulkDeletion ? 'bulk' : 'single'}
                          bulkType={message.bulkType}
                        />
                      );
                    }

                    return (
                      // <View
                      <TouchableOpacity
                        key={index}
                        activeOpacity={0.7}
                        delayLongPress={300}
                        onLongPress={() => {
                          console.log(
                            'Long pressed:',
                            message.id,
                            selectedMessage?.id === message.id,
                          );
                          !message.isDeleted && setSelectedMessage(message);
                        }}
                        // onLongPress={() => setSelectedMessage(message)}
                        onPress={() => {
                          console.log('Pressed:', message.id);
                          if (selectedMessage) setSelectedMessage(null);
                        }}
                        style={[
                          styles.messageContainer,
                          isCurrentUser
                            ? styles.messageRight
                            : styles.messageLeft,
                          isSelected && {
                            backgroundColor: 'rgba(21, 132, 250, 0.5)',
                          },
                        ]}>
                        {!isCurrentUser && (
                          <View style={styles.userDetailsContainer}>
                            {/* User Image */}
                            {message.user.image && (
                              <Image
                                source={{uri: message.user.image}}
                                style={styles.userImage}
                              />
                            )}
                            <Text style={styles.username}>
                              {message.user.username}
                            </Text>
                          </View>
                        )}

                        {/* Reply Display */}
                        {message.replyToMessage && (
                          <View style={styles.replyDisplay}>
                            <Text style={styles.replyDisplayLabel}>
                              {message.replyToMessage.user?.username ||
                                'Unknown'}
                            </Text>
                            <Text
                              style={styles.replyDisplayText}
                              numberOfLines={2}>
                              {message.replyToMessage.content || 'Message'}
                            </Text>
                          </View>
                        )}

                        {/* Voice Message */}
                        {message.messageType === 'voice' && message.voiceUrl ? (
                          <VoiceMessage
                            audioUrl={message.voiceUrl}
                            duration={message.voiceDuration || 0}
                            isOwnMessage={isCurrentUser}
                            messageTime={format(
                              parseISO(message.createdAt),
                              'HH:mm',
                            )}
                          />
                        ) : (
                          <Text
                            style={[
                              styles.messageText,
                              isCurrentUser
                                ? styles.messageTextRight
                                : styles.messageTextLeft,
                            ]}>
                            {message.content}
                          </Text>
                        )}
                        {!message.isDeleted &&
                          message.images &&
                          message.images.map((uri: any, idx: number) => (
                            <Image
                              key={idx}
                              source={{uri: uri?.imageUrl}}
                              style={styles.messageImage}
                            />
                          ))}
                        <Text
                          style={[
                            styles.messageTime,
                            isCurrentUser
                              ? styles.messageTimeRight
                              : styles.messageTimeLeft,
                          ]}>
                          {message.isEdited && (
                            <Text style={styles.editedText}> (Edited) </Text>
                          )}
                          {format(parseISO(message.createdAt), 'hh:mm a')}
                        </Text>
                        {/* </View> */}
                      </TouchableOpacity>
                    );
                  })}
                </View>
              );
            }}
            onContentSizeChange={() =>
              flatListRef.current?.scrollToEnd({animated: true})
            }
          />
        </View>

        {/* Input Section */}
        {isBlocked ? (
          <View style={styles.blockedContainer}>
            <View style={styles.blockedMessageContainer}>
              <Ionicons name="ban" size={24} color="#ff4444" />
              <Text style={styles.blockedText}>
                You have been blocked from sending messages in this group by an
                admin.
              </Text>
            </View>
          </View>
        ) : (
          <>
            {/* Reply Preview */}
            {replyingTo && (
              <View style={styles.replyPreview}>
                <View style={styles.replyContent}>
                  <Text style={styles.replyLabel}>
                    Replying to {replyingTo.user?.username || 'Unknown'}
                  </Text>
                  <Text style={styles.replyText} numberOfLines={2}>
                    {replyingTo.content || 'Message'}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => setReplyingTo(null)}
                  style={styles.replyCloseButton}>
                  <Ionicons name="close" size={20} color="#666" />
                </TouchableOpacity>
              </View>
            )}

            <View style={styles.inputContainer}>
              {isRecordingVoice ? (
                <VoiceRecorder
                  onRecordingComplete={handleVoiceRecordingComplete}
                  onCancel={handleVoiceRecordingCancel}
                  isRecording={isRecordingVoice}
                  setIsRecording={setIsRecordingVoice}
                  isSending={isSendingMessage}
                />
              ) : (
                <>
                  <TextInput
                    style={[
                      styles.input,
                      isCurrentUserBlocked && styles.disabledInput,
                    ]}
                    placeholder={
                      isCurrentUserBlocked
                        ? 'You are blocked from sending messages'
                        : 'Type a message'
                    }
                    placeholderTextColor={
                      isCurrentUserBlocked ? '#ff6b6b' : '#888'
                    }
                    value={newMessage}
                    onChangeText={setNewMessage}
                    editable={!isCurrentUserBlocked}
                  />
                  <TouchableOpacity
                    onPress={isCurrentUserBlocked ? undefined : pickImages}
                    disabled={isCurrentUserBlocked}>
                    <Ionicons
                      size={24}
                      style={[
                        styles.attachButton,
                        isCurrentUserBlocked && styles.disabledButton,
                      ]}
                      name="attach-outline"
                    />
                  </TouchableOpacity>

                  {/* Voice Recording Button */}
                  <TouchableOpacity
                    onPress={
                      isCurrentUserBlocked
                        ? undefined
                        : () => setIsRecordingVoice(true)
                    }
                    disabled={isCurrentUserBlocked}
                    style={styles.voiceButton}>
                    <Ionicons
                      name="mic"
                      size={24}
                      color={isCurrentUserBlocked ? '#ccc' : '#007bff'}
                    />
                  </TouchableOpacity>

                  <Button
                    color={isCurrentUserBlocked ? '#ccc' : '#003580'}
                    title={
                      isSendingMessage
                        ? 'Sending...'
                        : isEditMode
                        ? 'Edit'
                        : 'Send'
                    }
                    onPress={isEditMode ? handleEdit : handleSendMessage}
                    disabled={isCurrentUserBlocked || isSendingMessage}
                  />
                </>
              )}
            </View>
          </>
        )}

        {/* Image Preview */}
        {images.length > 0 && (
          <View style={styles.imagePreviewContainer}>
            {images.map((uri, index) => (
              <Image key={index} source={{uri}} style={styles.imagePreview} />
            ))}
          </View>
        )}
      </KeyboardAvoidingView>
    </PermissionGuard>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#e5ddd5', // WhatsApp chat background
  },
  chatContainer: {
    flex: 1,
    paddingHorizontal: 8,
    paddingVertical: 5,
  },
  dateHeader: {
    fontSize: 12,
    fontWeight: '500',
    color: '#667781',
    textAlign: 'center',
    marginVertical: 15,
    backgroundColor: '#ffffff',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    alignSelf: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageContainer: {
    marginVertical: 2,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    maxWidth: '85%',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  messageLeft: {
    alignSelf: 'flex-start',
    backgroundColor: '#ffffff',
    marginLeft: 8,
    borderBottomLeftRadius: 4,
  },
  messageRight: {
    alignSelf: 'flex-end',
    backgroundColor: '#e3f2fd', // Light blue for sent messages
    marginRight: 8,
    borderBottomRightRadius: 4,
  },
  username: {
    fontWeight: '600',
    marginBottom: 2,
    color: '#007bff', // Primary blue for usernames
    fontSize: 13,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageTextLeft: {
    color: '#111b21', // Dark text for received messages (white background)
  },
  messageTextRight: {
    color: '#111b21', // Dark text for sent messages (green background)
  },
  messageImage: {
    width: 200,
    height: 200,
    marginVertical: 5,
    borderRadius: 12,
  },
  messageTime: {
    fontSize: 11,
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  messageTimeLeft: {
    color: '#667781', // Gray for received messages
  },
  messageTimeRight: {
    color: '#667781', // Gray for sent messages
  },
  editedText: {
    fontSize: 10,
    color: '#667781',
    fontStyle: 'italic',
  },
  inputContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingVertical: 8,
    alignItems: 'center',
    backgroundColor: '#f0f2f5',
  },
  input: {
    flex: 1,
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 8,
    borderRadius: 25,
    color: '#111b21',
    fontSize: 16,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: '#e9edef',
  },
  disabledInput: {
    backgroundColor: '#f5f5f5',
    color: '#999',
    borderColor: '#ff6b6b',
    borderWidth: 2,
  },
  disabledButton: {
    color: '#ccc',
    opacity: 0.5,
  },
  blockedContainer: {
    padding: 15,
    backgroundColor: '#fff5f5',
    borderTopWidth: 1,
    borderTopColor: '#ffcccc',
  },
  blockedMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#ffe6e6',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ff9999',
  },
  blockedText: {
    marginLeft: 10,
    color: '#cc0000',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    flex: 1,
  },
  attachButton: {
    color: '#667781',
    marginRight: 8,
    padding: 8,
  },
  imagePreviewContainer: {
    marginVertical: 10,
    alignItems: 'center',
    backgroundColor: '#f0f2f5',
    paddingVertical: 10,
  },
  imagePreview: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginHorizontal: 4,
  },
  userDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 3,
  },
  userImage: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
    backgroundColor: '#e9edef',
  },
  replyPreview: {
    backgroundColor: '#ffffff',
    borderLeftWidth: 4,
    borderLeftColor: '#007bff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginHorizontal: 8,
    marginBottom: 4,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: 12,
    color: '#007bff',
    fontWeight: '600',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 13,
    color: '#667781',
  },
  replyCloseButton: {
    padding: 6,
  },
  replyDisplay: {
    backgroundColor: 'rgba(0, 123, 255, 0.1)',
    borderLeftWidth: 3,
    borderLeftColor: '#007bff',
    paddingHorizontal: 8,
    paddingVertical: 6,
    marginBottom: 4,
    borderRadius: 6,
  },
  replyDisplayLabel: {
    fontSize: 11,
    color: '#007bff',
    fontWeight: '600',
    marginBottom: 1,
  },
  replyDisplayText: {
    fontSize: 12,
    color: '#667781',
    fontStyle: 'italic',
  },
  voiceButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f2f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
});

export default ChatForumScreen;
