import {useNavigation} from '@react-navigation/native';
import React, {useEffect, useRef, useState} from 'react';
import {
  StyleSheet,
  View,
  Image,
  Text,
  TouchableOpacity,
  Modal,
} from 'react-native';
import ImageView from 'react-native-image-viewing';
import normalize from '../types/utiles';
import {flattenDeep} from 'lodash';
import {deletePost, likePost, unlikePost} from '../services/postServices';
import RBSheet from 'react-native-raw-bottom-sheet';
import AntDesign from 'react-native-vector-icons/AntDesign';
import {useSelector} from 'react-redux';
import Comment from './Comment';
import PostShareBottomSheet from './PostShareBottomSheet';
import {TextStyles, Colors as FontColors} from '../styles/fonts';
import {socket} from '../services/socket';
import {ALERT_TYPE, Dialog} from './CustomAlert';
import Likes from './Likes';
import PermissionGuard from './PermissionGuard';

type PostProps = {
  companyLogo: string;
  companyName: string;
  position: string;
  timeStamp: string;
  postText: string;
  postImages: string[];
  postId: number;
  userId: number;
  postData: any;
  likesCount: number;
  commentsCount: number;
  fetchPosts: () => void;
};

const PostScreen: React.FC<PostProps> = ({
  companyLogo,
  companyName,
  position,
  timeStamp,
  postText,
  postImages,
  postId,
  userId,
  postData,
  likesCount,
  commentsCount,
  fetchPosts,
}) => {
  const [isImageViewVisible, setIsImageViewVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const formattedImages = postImages.map(uri => ({uri}));
  const refRBSheet = useRef<any>(null);
  const refRBSheet02 = useRef<any>(null);
  const refRBSheet03 = useRef<any>(null);
  const refRBSheetShare = useRef<any>(null);
  const [postImagesIds, setPostImagesIds] = useState<number[]>([]);
  const navigation = useNavigation();
  const loginUserId = useSelector((state: any) => state.auth.userId);
  const [likePostId, setLikePostId] = useState<any>(null);

  const handleImagePress = (index: number) => {
    setSelectedImageIndex(index);
    setIsImageViewVisible(true);
  };
  const handleMorePress = (postData: any) => {
    const postImagesIds: number[] = flattenDeep(
      postData?.images?.map((i: any) => i.id),
    );
    setPostImagesIds(postImagesIds);
    refRBSheet02.current?.open();
  };

  const handleEditPress = () => {
    refRBSheet02.current?.close();
    // @ts-ignore
    navigation.navigate('UpdatePost', {
      postId,
      userId,
      postImages,
      postImagesIds,
      postText,
    });
  };

  const handleDeletePress = async () => {
    try {
      await deletePost(postId, userId);
      Dialog.show({
        type: ALERT_TYPE.INFO,
        title: 'Post Deleted',
        textBody: 'The post has been successfully deleted.',
        button: 'OK',
        onPressButton: () => {
          Dialog.hide();
          refRBSheet02.current?.close();
        },
      });
      fetchPosts();
    } catch (error) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Failed to delete the post. Please try again.',
        button: 'OK',
        onPressButton: () => {
          Dialog.hide();
          refRBSheet02.current?.close();
        },
      });
      console.error(error);
    }
  };

  const handleLike = async () => {
    try {
      if (isLiked) {
        setIsLiked(false);
        await unlikePost(postId, loginUserId);
        fetchPosts();
      } else {
        setIsLiked(true);
        await likePost(postId, loginUserId);
        fetchPosts();
        // Socket emission is now handled by the backend
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleDisplayLikes = () => {
    refRBSheet03.current?.open();
  };

  const handleSharePost = () => {
    refRBSheetShare.current?.open();
  };

  const formatLikesText = () => {
    if (likesCount === 0) return '';

    const likedUsers = postData.likedUsers || [];

    if (likesCount === 1) {
      const firstName = likedUsers[0]?.name || 'Someone';
      return firstName;
    } else if (likesCount === 2) {
      const firstName = likedUsers[0]?.name || 'Someone';
      const secondName = likedUsers[1]?.name || 'Someone';
      return `${firstName} and ${secondName}`;
    } else {
      const firstName = likedUsers[0]?.name || 'Someone';
      const othersCount = likesCount - 1;
      return `${firstName} and ${othersCount} others`;
    }
  };

  useEffect(() => {
    const userLiked = postData.likedUsers.some(
      (user: {userId: any}) => user.userId === loginUserId,
    );
    setIsLiked(userLiked);

    // Join user room to receive notifications
    socket.emit('joinUserRoom', loginUserId);

    // Listen for new likes on this post
    socket.on('newLike', data => {
      if (data.postId === postId) {
        fetchPosts(); // Refresh posts to get updated like count
      }
    });

    // Listen for new comments on this post
    socket.on('newComment', data => {
      if (data.postId === postId) {
        fetchPosts(); // Refresh posts to get updated comment count
      }
    });

    return () => {
      socket.off('newLike');
      socket.off('newComment');
    };
  }, [postData, loginUserId, postId, fetchPosts]);

  const renderImages = () => {
    if (postImages.length === 1) {
      return (
        <TouchableOpacity
          style={styles.singleImageContainer}
          onPress={() => handleImagePress(0)}>
          <Image source={{uri: postImages[0]}} style={styles.singleImage} />
        </TouchableOpacity>
      );
    } else if (postImages.length === 2) {
      return (
        <View style={styles.twoImagesContainer}>
          {formattedImages.map((image, index) => (
            <TouchableOpacity
              key={`two-${index}`}
              style={styles.twoImageWrapper}
              onPress={() => handleImagePress(index)}>
              <Image source={{uri: image.uri}} style={styles.twoImage} />
            </TouchableOpacity>
          ))}
        </View>
      );
    } else if (postImages.length === 3) {
      return (
        <View style={styles.threeImagesContainer}>
          {formattedImages.map((image, index) => (
            <TouchableOpacity
              key={`three-${index}`}
              style={styles.threeImageWrapper}
              onPress={() => handleImagePress(index)}>
              <Image source={{uri: image.uri}} style={styles.threeImage} />
            </TouchableOpacity>
          ))}
        </View>
      );
    } else if (postImages.length > 3) {
      return (
        <View style={styles.moreThanThreeImagesContainer}>
          {formattedImages.slice(0, 3).map((image, index) => (
            <TouchableOpacity
              key={`more-${index}`}
              style={styles.moreThanThreeImageWrapper}
              onPress={() => handleImagePress(index)}>
              <Image
                source={{uri: image.uri}}
                style={styles.moreThanThreeImage}
              />
            </TouchableOpacity>
          ))}
          <TouchableOpacity
            style={styles.moreImagesOverlay}
            onPress={() => handleImagePress(3)}>
            <Text style={styles.moreImagesText}>
              +{postImages.length - 3} more
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  };

  const handlePressProfile = () => {
    if (loginUserId === postData.userId) {
      // Navigate to own profile
      navigation.navigate('Profile');
    } else {
      // Navigate to other user's profile
      navigation.navigate('ViewUserProfile', {
        userId: postData.userId,
        userName: companyName,
      });
    }
  };

  return (
    <View style={styles.postContainer}>
      <View style={styles.postHeader}>
        <TouchableOpacity onPress={handlePressProfile}>
          <Image source={{uri: companyLogo}} style={styles.companyLogo} />
        </TouchableOpacity>
        {/* <Image source={{ uri: companyLogo }} style={styles.companyLogo} /> */}
        <View style={styles.postInfo}>
          <TouchableOpacity onPress={handlePressProfile}>
            <Text style={styles.companyName}>{companyName}</Text>
          </TouchableOpacity>
          <Text style={styles.postMeta}>{position}</Text>
          <Text style={styles.timeStamp}>{timeStamp}</Text>
        </View>
        {postData.userId === loginUserId && (
          <TouchableOpacity onPress={() => handleMorePress(postData)}>
            <Text style={styles.moreButton}>•••</Text>
          </TouchableOpacity>
        )}
      </View>

      <Text style={styles.postText}>{postText}</Text>

      {renderImages()}

      <ImageView
        images={formattedImages}
        imageIndex={selectedImageIndex}
        visible={isImageViewVisible}
        onRequestClose={() => setIsImageViewVisible(false)}
      />

      <View style={styles.container}>
        {(likesCount > 0 || commentsCount > 0) && (
          <View style={styles.reactionContainer}>
            <View
              style={[
                styles.reactionCount,
                {
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                },
              ]}>
              <Text style={styles.reactionTextLikes}>
                {likesCount > 0 ? (
                  <TouchableOpacity
                    onPress={() => handleDisplayLikes()}
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View style={[styles.iconWrapper, {marginRight: 6}]}>
                      <AntDesign name="like1" size={10} color="#ffffff" />
                    </View>
                    <Text style={{color: '#333333', fontSize: 13}}>
                      {formatLikesText()}
                    </Text>
                  </TouchableOpacity>
                ) : (
                  <></>
                )}
              </Text>
              {commentsCount > 0 && (
                <TouchableOpacity
                  onPress={() => refRBSheet.current?.open()}
                  style={styles.reactionTextComment}>
                  <Text style={styles.reactionTextComment}>
                    {commentsCount} Comments
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}

        <View style={styles.actionButtons}>
          <PermissionGuard permission="LIKE_POST">
            <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
              <View style={{display: 'flex', flexDirection: 'row', gap: 8}}>
                <Text style={{color: isLiked ? 'blue' : 'black'}}>
                  <AntDesign
                    name="like1"
                    size={17}
                    style={{
                      color: isLiked ? 'blue' : '#333333',
                    }}
                  />
                </Text>
                <TouchableOpacity>
                  <Text
                    style={{
                      color: isLiked ? 'blue' : '#333333',
                    }}>
                    {isLiked ? 'Like' : 'Like'}
                  </Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </PermissionGuard>
          <PermissionGuard permission="COMMENT_ON_POST">
            <TouchableOpacity
              onPress={() => refRBSheet.current?.open()}
              style={styles.actionButton}>
              <AntDesign name="message1" size={17} color="#333333" />
              <Text style={{color: '#333333'}}>Comments</Text>
            </TouchableOpacity>
          </PermissionGuard>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleSharePost}>
            <AntDesign name="sharealt" size={18} color="#333333" />
            <Text style={{color: '#333333'}}>Share</Text>
          </TouchableOpacity>
        </View>
      </View>

      <RBSheet
        ref={refRBSheet02}
        height={150}
        draggable
        openDuration={250}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: '#F8F9FA',
            paddingHorizontal: 20,
            paddingTop: 10,
          },
          draggableIcon: {
            backgroundColor: '#A0A0A0',
            width: 40,
            height: 4,
          },
        }}
        customModalProps={{
          animationType: 'slide',
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: true,
        }}>
        <TouchableOpacity
          onPress={handleEditPress}
          style={{
            paddingVertical: 14,
            width: '100%',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#FFF',
            borderRadius: 12,
            marginBottom: 10,
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 4},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          }}
          activeOpacity={0.7}>
          <AntDesign name="edit" size={24} color="#002157" />
          <Text style={{fontSize: 18, color: '#333', marginLeft: 10}}>
            Edit
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleDeletePress}
          style={{
            paddingVertical: 14,
            width: '100%',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#FFF',
            borderRadius: 12,
            shadowColor: '#000',
            shadowOffset: {width: 0, height: 4},
            shadowOpacity: 0.1,
            shadowRadius: 4,
          }}
          activeOpacity={0.7}>
          <AntDesign name="delete" size={24} color="#DC3545" />
          <Text style={{fontSize: 18, color: '#333', marginLeft: 10}}>
            Delete
          </Text>
        </TouchableOpacity>
      </RBSheet>
      <RBSheet
        draggable
        ref={refRBSheet03}
        height={600}
        openDuration={250}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: '#F8F9FA',
            paddingHorizontal: 20,
            paddingTop: 10,
          },
          draggableIcon: {
            backgroundColor: '#A0A0A0',
            width: 40,
            height: 4,
          },
        }}
        customModalProps={{
          animationType: 'slide',
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: true,
        }}>
        <Likes postData={postData} likesCount={likesCount} />
      </RBSheet>

      <RBSheet
        draggable
        ref={refRBSheet}
        closeOnPressMask={true}
        height={600}
        useNativeDriver={false}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          },
          draggableIcon: {
            backgroundColor: '#000',
            width: 40,
            height: 4,
          },
        }}
        customModalProps={{
          animationType: 'slide',
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}>
        <Comment postData={postData} fetchPosts={fetchPosts} />
      </RBSheet>

      <RBSheet
        ref={refRBSheetShare}
        height={500}
        draggable
        openDuration={250}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: '#fff',
          },
          draggableIcon: {
            backgroundColor: '#A0A0A0',
            width: 40,
            height: 4,
          },
        }}
        customModalProps={{
          animationType: 'slide',
          statusBarTranslucent: true,
        }}
        customAvoidingViewProps={{
          enabled: false,
        }}>
        <PostShareBottomSheet
          postData={{
            id: postId,
            authorName: companyName,
            authorImage: companyLogo,
            postText: postText,
            postImages: postImages,
            timeStamp: timeStamp,
          }}
          onClose={() => refRBSheetShare.current?.close()}
        />
      </RBSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  postContainer: {
    backgroundColor: '#ffffff',
    marginTop: 8,
  },
  postHeader: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 18,
    alignItems: 'flex-start',
  },
  companyLogo: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  postInfo: {
    flex: 1,
    marginLeft: 8,
  },
  companyName: {
    ...TextStyles.username,
    color: FontColors.text.primary,
  },
  postMeta: {
    ...TextStyles.caption,
    color: FontColors.text.secondary,
  },
  timeStamp: {
    ...TextStyles.timestamp,
    color: FontColors.text.secondary,
  },
  moreButton: {
    fontSize: 20,
    color: '#65676b',
  },
  postText: {
    ...TextStyles.postContent,
    color: FontColors.text.primary,
    padding: 12,
  },
  singleImageContainer: {
    marginTop: 12,
  },
  singleImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f2f5',
  },
  twoImagesContainer: {
    flexDirection: 'row',
    marginTop: 12,
  },
  twoImageWrapper: {
    flex: 1,
    marginHorizontal: 1,
  },
  twoImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f2f5',
  },
  threeImagesContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    marginTop: 12,
  },
  threeImageWrapper: {
    flex: 1,
    marginHorizontal: 1,
  },
  threeImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f2f5',
  },
  moreThanThreeImagesContainer: {
    flexDirection: 'row',
    marginTop: 12,
    position: 'relative',
  },
  moreThanThreeImageWrapper: {
    flex: 1,
    marginHorizontal: 1,
  },
  moreThanThreeImage: {
    width: '100%',
    height: 200,
    backgroundColor: '#f0f2f5',
  },
  moreImagesOverlay: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: '33%',
    height: 200,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreImagesText: {
    fontSize: 14,
    color: '#ffffff',
    fontWeight: 'bold',
  },
  actionButtons: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#e4e6eb',
    padding: 3,
  },
  actionButton: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    gap: 8,
    justifyContent: 'center',
  },
  container: {
    backgroundColor: '#ffffff',
    paddingVertical: 4,
  },
  reactionContainer: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 16,
    // paddingTop: 8,
    paddingBottom: 12,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  reactionCount: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingTop: 6,
  },
  reactionTextLikes: {
    color: '#65676B',
    fontSize: 14,
  },
  reactionTextComment: {
    color: '#333333',
    fontSize: 14,
    justifyContent: 'flex-end',
  },
  separator: {
    height: 1,
    backgroundColor: '#E4E6EB',
    marginBottom: 4,
  },
  likeText: {
    fontSize: 16,
  },
  actionText: {
    color: '#65676B',
    fontSize: 14,
    fontWeight: '600',
  },
  likedText: {
    color: '#0000FF',
  },
  iconWrapper: {
    backgroundColor: '#0000FF',
    borderRadius: 99,
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default PostScreen;
