# Simple Mobile App Permissions Guide

This guide shows how to use the 4 basic permissions: `ADD_POST`, `COMMENT_ON_POST`, `LIKE_POST`, and `ENTER_CHAT_FORUM`.

## 🚀 How It Works

### **1. Automatic Setup**
- ✅ **Login**: Permissions are fetched from backend automatically
- ✅ **Cache**: Permissions are saved locally for offline use
- ✅ **Startup**: Cached permissions are loaded when app starts

### **2. Permission Flow**
```
User Login → Fetch Permissions from API → Cache Locally → UI Updates
```

## 🎯 The 4 Permissions

### **ADD_POST**
- **Who has it**: USER, HR, ADMIN roles
- **Who doesn't**: GUEST role
- **What it controls**: Add Post button in HomeScreen

### **LIKE_POST**
- **Who has it**: USER, HR, ADMIN roles
- **Who doesn't**: GUEST role
- **What it controls**: Like button on posts

### **COMMENT_ON_POST**
- **Who has it**: USER, HR, ADMIN roles
- **Who doesn't**: GUEST role
- **What it controls**: Comment button on posts

### **ENTER_CHAT_FORUM**
- **Who has it**: USER, HR, ADMIN roles
- **Who doesn't**: GUEST role
- **What it controls**: Access to chat forums and group messaging

## 🔧 Implementation Examples

### **1. Protecting UI Elements**
```typescript
import PermissionGuard from '../components/PermissionGuard';

// Hide Add Post button for users without permission
<PermissionGuard permission="ADD_POST">
  <TouchableOpacity onPress={() => navigation.navigate('AddPost')}>
    <Text>Add Post</Text>
  </TouchableOpacity>
</PermissionGuard>

// Hide Like button for users without permission
<PermissionGuard permission="LIKE_POST">
  <TouchableOpacity onPress={handleLike}>
    <Text>Like</Text>
  </TouchableOpacity>
</PermissionGuard>

// Hide Comment button for users without permission
<PermissionGuard permission="COMMENT_ON_POST">
  <TouchableOpacity onPress={openComments}>
    <Text>Comments</Text>
  </TouchableOpacity>
</PermissionGuard>

// Protect entire chat screen
<PermissionGuard permission="ENTER_CHAT_FORUM" showMessage={true}>
  <ChatForumScreen />
</PermissionGuard>
```

### **2. Using Permission Hooks**
```typescript
import { usePermissions } from '../hooks/usePermissions';

const MyComponent = () => {
  const { canAddPost, canLikePost, canCommentOnPost, canEnterChatForum } = usePermissions();

  return (
    <View>
      {canAddPost && <Button title="Add Post" />}
      {canLikePost && <Button title="Like" />}
      {canCommentOnPost && <Button title="Comment" />}
      {canEnterChatForum && <Button title="Enter Chat" />}
    </View>
  );
};
```

### **3. Manual Permission Checks**
```typescript
import { canAddPost, canLikePost, canCommentOnPost } from '../services/permissionService';

// Check permissions manually
if (canAddPost()) {
  // Show add post functionality
}

if (canLikePost()) {
  // Show like functionality
}

if (canCommentOnPost()) {
  // Show comment functionality
}
```

## 📱 What Users See

### **GUEST Users (No Permissions)**
- ❌ **No Add Post button** in HomeScreen
- ❌ **No Like button** on posts
- ❌ **No Comment button** on posts
- ❌ **Cannot access chat forums** - shows "Access Denied" message
- ✅ **Can view posts** and content

### **USER/HR/ADMIN Users (All Permissions)**
- ✅ **Add Post button** visible in HomeScreen
- ✅ **Like button** visible on posts
- ✅ **Comment button** visible on posts
- ✅ **Can access chat forums** and group messaging
- ✅ **Full functionality** available

## 🔍 Testing

### **Test with GUEST User:**
1. Login with a GUEST role user
2. Check HomeScreen - Add Post button should be hidden
3. Check posts - Like and Comment buttons should be hidden

### **Test with USER:**
1. Login with a USER role user
2. Check HomeScreen - Add Post button should be visible
3. Check posts - Like and Comment buttons should be visible

## 🛠️ Console Logs

The system logs important events:

```
✅ Permissions initialized successfully
✅ Permissions loaded from cache on app startup
✅ Loaded 3 permissions for USER: ["ADD_POST", "COMMENT_ON_POST", "LIKE_POST"]
Permission denied: ADD_POST (role: GUEST)
```

## 🎉 Already Protected

### **HomeScreen**
- ✅ Add Post button is protected with `ADD_POST` permission

### **Post Component**
- ✅ Like button is protected with `LIKE_POST` permission
- ✅ Comment button is protected with `COMMENT_ON_POST` permission

### **Chat System**
- ✅ ChatForumScreen is protected with `ENTER_CHAT_FORUM` permission
- ✅ Group navigation shows "Access Denied" for users without permission

## 🔄 How to Add More Protection

### **Protect Any UI Element:**
```typescript
<PermissionGuard permission="ADD_POST">
  <YourComponent />
</PermissionGuard>
```

### **Protect Entire Screens:**
```typescript
// Create a withPermission HOC if needed for full screen protection
```

### **Add More Permissions:**
Just add them to the `MobilePermission` type in `permissionService.ts`:
```typescript
export type MobilePermission = 'ADD_POST' | 'COMMENT_ON_POST' | 'LIKE_POST' | 'NEW_PERMISSION';
```

## ✅ Benefits

- 🔒 **Secure**: Only authorized users see features
- 🚀 **Simple**: Easy to implement with PermissionGuard
- 📱 **User-Friendly**: Features are hidden, not disabled
- 🔄 **Automatic**: Works with existing login system
- 💾 **Offline**: Cached permissions work without internet

The mobile app now has simple, effective permission control for the 3 key features! 🎯
