import React, {useState, useRef, useEffect} from 'react';
import {View, Text, TouchableOpacity, StyleSheet, Alert} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import Ionicons from 'react-native-vector-icons/Ionicons';
// import Slider from '@react-native-community/slider';

interface VoiceMessageProps {
  audioUrl: string;
  duration: number;
  isOwnMessage: boolean;
  messageTime: string;
}

const VoiceMessage: React.FC<VoiceMessageProps> = ({
  audioUrl,
  duration,
  isOwnMessage,
  messageTime,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentPositionSec, setCurrentPositionSec] = useState(0);
  const [currentDurationSec, setCurrentDurationSec] = useState(duration);
  const [playTime, setPlayTime] = useState('00:00');
  const [totalDuration, setTotalDuration] = useState(formatTime(duration));
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;

  useEffect(() => {
    return () => {
      // Cleanup on unmount
      if (isPlaying) {
        audioRecorderPlayer.stopPlayer();
        audioRecorderPlayer.removePlayBackListener();
      }
    };
  }, [audioRecorderPlayer, isPlaying]);

  function formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  }

  const startPlayer = async () => {
    try {
      console.log('Starting player for:', audioUrl);
      const msg = await audioRecorderPlayer.startPlayer(audioUrl);

      audioRecorderPlayer.addPlayBackListener(e => {
        setCurrentPositionSec(e.currentPosition);
        setCurrentDurationSec(e.duration);
        setPlayTime(formatTime(Math.floor(e.currentPosition / 1000)));
        setTotalDuration(formatTime(Math.floor(e.duration / 1000)));

        // Auto stop when finished
        if (e.currentPosition >= e.duration) {
          setIsPlaying(false);
          setCurrentPositionSec(0);
          setPlayTime('00:00');
          audioRecorderPlayer.removePlayBackListener();
        }
      });

      setIsPlaying(true);
      console.log('Player started:', msg);
    } catch (error) {
      console.error('Start player error:', error);
      Alert.alert('Error', 'Failed to play voice message');
    }
  };

  const pausePlayer = async () => {
    try {
      await audioRecorderPlayer.pausePlayer();
      setIsPlaying(false);
    } catch (error) {
      console.error('Pause player error:', error);
    }
  };

  const resumePlayer = async () => {
    try {
      await audioRecorderPlayer.resumePlayer();
      setIsPlaying(true);
    } catch (error) {
      console.error('Resume player error:', error);
    }
  };

  const stopPlayer = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setIsPlaying(false);
      setCurrentPositionSec(0);
      setPlayTime('00:00');
    } catch (error) {
      console.error('Stop player error:', error);
    }
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      pausePlayer();
    } else if (currentPositionSec > 0) {
      resumePlayer();
    } else {
      startPlayer();
    }
  };

  const progress =
    currentDurationSec > 0
      ? (currentPositionSec / currentDurationSec) * 100
      : 0;

  return (
    <View
      style={[
        styles.container,
        isOwnMessage ? styles.ownMessage : styles.otherMessage,
      ]}>
      <TouchableOpacity
        style={[
          styles.playButton,
          isOwnMessage ? styles.ownPlayButton : styles.otherPlayButton,
        ]}
        onPress={handlePlayPause}
        activeOpacity={0.7}>
        <Ionicons
          name={isPlaying ? 'pause' : 'play'}
          size={20}
          color={isOwnMessage ? '#fff' : '#007bff'}
        />
      </TouchableOpacity>

      <View style={styles.audioInfo}>
        <View style={styles.waveformContainer}>
          <View style={styles.progressBarContainer}>
            <View
              style={[
                styles.progressBar,
                {
                  width: `${progress}%`,
                  backgroundColor: isOwnMessage ? '#fff' : '#007bff',
                },
              ]}
            />
          </View>
        </View>

        <View style={styles.timeContainer}>
          <Text
            style={[
              styles.timeText,
              isOwnMessage ? styles.ownTimeText : styles.otherTimeText,
            ]}>
            {playTime} / {totalDuration}
          </Text>
        </View>
      </View>

      <Text
        style={[
          styles.messageTime,
          isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime,
        ]}>
        {messageTime}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    maxWidth: '80%',
    minWidth: 200,
  },
  ownMessage: {
    backgroundColor: '#007bff',
    alignSelf: 'flex-end',
  },
  otherMessage: {
    backgroundColor: '#f0f2f5',
    alignSelf: 'flex-start',
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  ownPlayButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  otherPlayButton: {
    backgroundColor: '#e3f2fd',
  },
  audioInfo: {
    flex: 1,
    marginRight: 8,
  },
  waveformContainer: {
    height: 30,
    justifyContent: 'center',
  },
  progressBarContainer: {
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  timeContainer: {
    marginTop: 2,
  },
  timeText: {
    fontSize: 12,
    fontWeight: '500',
  },
  ownTimeText: {
    color: 'rgba(255,255,255,0.8)',
  },
  otherTimeText: {
    color: '#666',
  },
  messageTime: {
    fontSize: 11,
    alignSelf: 'flex-end',
  },
  ownMessageTime: {
    color: 'rgba(255,255,255,0.7)',
  },
  otherMessageTime: {
    color: '#999',
  },
});

export default VoiceMessage;
