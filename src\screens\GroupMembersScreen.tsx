import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  SafeAreaView,
  Image,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {NavigationProp} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import {RootStackParamList} from '../types';
import normalize from '../types/utiles';
import Colors from '../constants/Colors';
import {GroupService} from '../services/chatservice';

type GroupMembersScreenRouteProp = RouteProp<
  RootStackParamList,
  'GroupMembers'
>;
type GroupMembersScreenNavigationProp = NavigationProp<RootStackParamList>;

interface GroupMember {
  id: number;
  userId: number;
  groupId: number;
  isBlocked: boolean;
  blockedAt?: string;
  blockedBy?: number;
  createdAt: string;
  user: {
    id: number;
    username: string;
    email: string;
    image?: string;
    role: string;
    trainee?: {
      name: string;
      contactNo: string;
      nic: string;
    };
  };
}

const GroupMembersScreen: React.FC = () => {
  const navigation = useNavigation<GroupMembersScreenNavigationProp>();
  const route = useRoute<GroupMembersScreenRouteProp>();
  const {groupId, groupName, groupImage} = route.params;

  const [members, setMembers] = useState<GroupMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchGroupMembers();
  }, [groupId]);

  const fetchGroupMembers = async () => {
    try {
      setLoading(true);
      const response = await GroupService.getGroupMembers(groupId);

      if (response.success) {
        setMembers(response.members || []);
      } else {
        Alert.alert('Error', 'Failed to fetch group members');
      }
    } catch (error) {
      console.error('Error fetching group members:', error);
      Alert.alert('Error', 'Failed to fetch group members');
    } finally {
      setLoading(false);
    }
  };

  const handleMemberPress = (member: GroupMember) => {
    // Navigate to user profile
    // @ts-ignore
    navigation.navigate('ViewUserProfile', {
      userId: member.userId,
      userName: member.user.trainee?.name || member.user.username,
    });
  };

  const renderMember = ({item}: {item: GroupMember}) => {
    const displayName = item.user.trainee?.name || item.user.username;
    const displayImage = item.user.image || 'https://via.placeholder.com/50';
    const isAdmin = item.user.role === 'ADMIN' || item.user.role === 'HR';

    return (
      <TouchableOpacity
        style={styles.memberContainer}
        onPress={() => handleMemberPress(item)}>
        <Image source={{uri: displayImage}} style={styles.memberImage} />

        <View style={styles.memberInfo}>
          <Text style={styles.memberName}>{displayName}</Text>
          <Text style={styles.memberEmail}>{item.user.email}</Text>
          {item.user.trainee?.contactNo && (
            <Text style={styles.memberContact}>
              {item.user.trainee.contactNo}
            </Text>
          )}
        </View>

        <View style={styles.memberActions}>
          {isAdmin && (
            <View style={styles.adminBadge}>
              <Text style={styles.adminText}>Admin</Text>
            </View>
          )}
          {item.isBlocked && (
            <View style={styles.blockedBadge}>
              <MaterialIcons name="block" size={16} color="#fff" />
              <Text style={styles.blockedText}>Blocked</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      {groupImage && (
        <Image source={{uri: groupImage}} style={styles.groupImage} />
      )}
      <Text style={styles.groupName}>{groupName}</Text>
      <Text style={styles.memberCount}>
        {members.length} {members.length === 1 ? 'member' : 'members'}
      </Text>
    </View>
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Group Members</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading members...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Group Members</Text>
      </View>

      <FlatList
        data={members}
        keyExtractor={item => item.id.toString()}
        renderItem={renderMember}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: normalize(15),
    paddingVertical: normalize(15),
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: 'bold',
    color: 'white',
    marginLeft: normalize(15),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: normalize(10),
    fontSize: normalize(16),
    color: Colors.primary,
  },
  listContainer: {
    paddingBottom: normalize(20),
  },
  headerContainer: {
    alignItems: 'center',
    backgroundColor: 'white',
    paddingVertical: normalize(20),
    marginBottom: normalize(10),
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  groupImage: {
    width: normalize(80),
    height: normalize(80),
    borderRadius: normalize(40),
    marginBottom: normalize(10),
  },
  groupName: {
    fontSize: normalize(20),
    fontWeight: 'bold',
    color: '#333',
    marginBottom: normalize(5),
  },
  memberCount: {
    fontSize: normalize(14),
    color: '#666',
  },
  memberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: normalize(15),
    paddingVertical: normalize(12),
    marginHorizontal: normalize(10),
    marginVertical: normalize(2),
    borderRadius: normalize(8),
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
  },
  memberImage: {
    width: normalize(50),
    height: normalize(50),
    borderRadius: normalize(25),
    marginRight: normalize(12),
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: normalize(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: normalize(2),
  },
  memberEmail: {
    fontSize: normalize(13),
    color: '#666',
    marginBottom: normalize(1),
  },
  memberContact: {
    fontSize: normalize(12),
    color: '#888',
  },
  memberActions: {
    alignItems: 'flex-end',
  },
  adminBadge: {
    backgroundColor: Colors.primary,
    paddingHorizontal: normalize(8),
    paddingVertical: normalize(4),
    borderRadius: normalize(12),
    marginBottom: normalize(4),
  },
  adminText: {
    fontSize: normalize(10),
    color: 'white',
    fontWeight: '600',
  },
  blockedBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f44336',
    paddingHorizontal: normalize(6),
    paddingVertical: normalize(2),
    borderRadius: normalize(10),
  },
  blockedText: {
    fontSize: normalize(9),
    color: 'white',
    marginLeft: normalize(2),
    fontWeight: '600',
  },
});

export default GroupMembersScreen;
