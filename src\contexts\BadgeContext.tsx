import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import {useSelector} from 'react-redux';
import {AppState, AppStateStatus} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {socket} from '../services/socket';

interface BadgeContextType {
  noticeBadgeCount: number;
  forumBadgeCount: number;
  setNoticeBadgeCount: (count: number) => void;
  setForumBadgeCount: (count: number) => void;
  clearNoticeBadge: () => void;
  clearForumBadge: () => void;
  incrementNoticeBadge: () => void;
  incrementForumBadge: () => void;
}

const BadgeContext = createContext<BadgeContextType | undefined>(undefined);

interface BadgeProviderProps {
  children: ReactNode;
}

export const BadgeProvider: React.FC<BadgeProviderProps> = ({children}) => {
  const [noticeBadgeCount, setNoticeBadgeCount] = useState(0);
  const [forumBadgeCount, setForumBadgeCount] = useState(0);
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState,
  );
  const userId = useSelector((state: any) => state.auth.userId);
  const isAuthenticated = useSelector(
    (state: any) => state.auth.isAuthenticated,
  );

  // Storage keys for persistence
  const NOTICE_BADGE_KEY = `notice_badge_${userId}`;
  const FORUM_BADGE_KEY = `forum_badge_${userId}`;

  // Load badge counts from storage when user logs in
  useEffect(() => {
    if (!isAuthenticated || !userId) return;

    const loadBadgeCounts = async () => {
      try {
        const savedNoticeBadge = await AsyncStorage.getItem(NOTICE_BADGE_KEY);
        const savedForumBadge = await AsyncStorage.getItem(FORUM_BADGE_KEY);

        if (savedNoticeBadge !== null) {
          const count = parseInt(savedNoticeBadge, 10);
          console.log('🔔 Loaded notice badge count from storage:', count);
          setNoticeBadgeCount(count);
        }

        if (savedForumBadge !== null) {
          const count = parseInt(savedForumBadge, 10);
          console.log('🔔 Loaded forum badge count from storage:', count);
          setForumBadgeCount(count);
        }
      } catch (error) {
        console.error('🔔 Error loading badge counts from storage:', error);
      }
    };

    loadBadgeCounts();
  }, [isAuthenticated, userId, NOTICE_BADGE_KEY, FORUM_BADGE_KEY]);

  // Handle app state changes
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log('🔔 App state changed from', appState, 'to', nextAppState);
      setAppState(nextAppState);

      // When app comes to foreground, reload badge counts from storage
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        console.log('🔔 App came to foreground, reloading badge counts');
        // Reload badge counts here if needed
      }
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );
    return () => subscription?.remove();
  }, [appState]);

  // Socket and notification setup
  useEffect(() => {
    if (!isAuthenticated || !userId) return;

    console.log('🔔 Setting up badge notification listeners for user:', userId);

    // Ensure we're in the user room (backup call after delay)
    setTimeout(() => {
      socket.emit('joinUserRoom', userId);
    }, 1000);

    // Listen for new notices
    const handleNewNotice = (data: any) => {
      console.log('🔔 NEW NOTICE SOCKET EVENT RECEIVED:', data);
      console.log(
        '🔔 Current notice badge count before increment:',
        noticeBadgeCount,
      );
      console.log('🔔 User ID:', userId);
      console.log('🔔 Is authenticated:', isAuthenticated);

      setNoticeBadgeCount(prev => {
        const newCount = prev + 1;
        console.log('🔔 NOTICE BADGE COUNT UPDATED:', prev, '→', newCount);
        return newCount;
      });
    };

    // Listen for new messages in forums
    const handleNewMessage = (data: any) => {
      console.log('🔔 New forum message received:', data);
      // Only increment if the message is not from the current user
      if (data.userId && data.userId.toString() !== userId.toString()) {
        setForumBadgeCount(prev => prev + 1);
      }
    };

    // Listen for new group messages
    const handleNewGroupMessage = (data: any) => {
      console.log('🔔 New group message received:', data);
      // Only increment if the message is not from the current user
      if (data.userId && data.userId.toString() !== userId.toString()) {
        setForumBadgeCount(prev => prev + 1);
      }
    };

    // Socket event listeners
    console.log('🔔 Socket connected:', socket.connected);

    // Monitor socket events (only for notices)
    socket.onAny((eventName, ...args) => {
      if (eventName.includes('notice') || eventName.includes('Notice')) {
        console.log('🔔 Socket received notice event:', eventName, args);
      }
    });

    socket.on('newNotice', handleNewNotice);
    socket.on('newMessage', handleNewMessage);
    socket.on('newGroupMessage', handleNewGroupMessage);
    socket.on('chatMessage', handleNewGroupMessage); // Alternative event name

    // Add additional potential notice event names
    socket.on('noticeCreated', handleNewNotice);
    socket.on('notice', handleNewNotice);
    socket.on('newNoticeCreated', handleNewNotice);

    // Cleanup listeners
    return () => {
      console.log('🔔 Cleaning up badge notification listeners');
      socket.offAny(); // Remove the debug listener
      socket.off('newNotice', handleNewNotice);
      socket.off('newMessage', handleNewMessage);
      socket.off('newGroupMessage', handleNewGroupMessage);
      socket.off('chatMessage', handleNewGroupMessage);
      socket.off('noticeCreated', handleNewNotice);
      socket.off('notice', handleNewNotice);
      socket.off('newNoticeCreated', handleNewNotice);
    };
  }, [isAuthenticated, userId]); // noticeBadgeCount is intentionally excluded to avoid infinite re-renders

  const clearNoticeBadge = async () => {
    console.log('🔔 Clearing notice badge');
    setNoticeBadgeCount(0);
    try {
      await AsyncStorage.setItem(NOTICE_BADGE_KEY, '0');
    } catch (error) {
      console.error('🔔 Error saving notice badge to storage:', error);
    }
  };

  const clearForumBadge = async () => {
    console.log('🔔 Clearing forum badge');
    setForumBadgeCount(0);
    try {
      await AsyncStorage.setItem(FORUM_BADGE_KEY, '0');
    } catch (error) {
      console.error('🔔 Error saving forum badge to storage:', error);
    }
  };

  const incrementNoticeBadge = async () => {
    setNoticeBadgeCount(prev => {
      const newCount = prev + 1;
      console.log('🔔 Notice badge count:', prev, '→', newCount);

      // Save to storage asynchronously
      AsyncStorage.setItem(NOTICE_BADGE_KEY, newCount.toString()).catch(
        error => {
          console.error('🔔 Error saving notice badge to storage:', error);
        },
      );

      return newCount;
    });
  };

  const incrementForumBadge = async () => {
    setForumBadgeCount(prev => {
      const newCount = prev + 1;
      console.log('🔔 Forum badge count:', prev, '→', newCount);

      // Save to storage asynchronously
      AsyncStorage.setItem(FORUM_BADGE_KEY, newCount.toString()).catch(
        error => {
          console.error('🔔 Error saving forum badge to storage:', error);
        },
      );

      return newCount;
    });
  };

  const value: BadgeContextType = {
    noticeBadgeCount,
    forumBadgeCount,
    setNoticeBadgeCount,
    setForumBadgeCount,
    clearNoticeBadge,
    clearForumBadge,
    incrementNoticeBadge,
    incrementForumBadge,
  };

  return (
    <BadgeContext.Provider value={value}>{children}</BadgeContext.Provider>
  );
};

export const useBadge = (): BadgeContextType => {
  const context = useContext(BadgeContext);
  if (!context) {
    throw new Error('useBadge must be used within a BadgeProvider');
  }
  return context;
};

export default BadgeContext;
