import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Linking,
  Share,
  Dimensions,
  Modal,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {format, parseISO} from 'date-fns';
import Colors from '../constants/Colors';
import apiClient from '../services/apiService';

const {width, height} = Dimensions.get('window');

interface NoticeDetailScreenProps {
  route: {
    params: {
      noticeId: number;
      notice?: any;
    };
  };
  navigation: any;
}

const NoticeDetailScreen: React.FC<NoticeDetailScreenProps> = ({
  route,
  navigation,
}) => {
  const {noticeId, notice: initialNotice} = route.params;
  const [notice, setNotice] = useState(initialNotice || null);
  const [loading, setLoading] = useState(!initialNotice);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    if (!initialNotice) {
      fetchNoticeDetails();
    }
  }, [noticeId]);

  const fetchNoticeDetails = async () => {
    try {
      setLoading(true);
      const noticeData = await apiClient.getJson(`notice/${noticeId}`);
      setNotice(noticeData);
    } catch (error) {
      console.error('Error fetching notice details:', error);
      Alert.alert('Error', 'Failed to load notice details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleEventUrlPress = async (url: string) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        Alert.alert('Error', 'Cannot open this URL');
      }
    } catch (error) {
      console.error('Error opening URL:', error);
      Alert.alert('Error', 'Failed to open URL');
    }
  };

  const handleShareEvent = async () => {
    if (!notice) return;

    try {
      let shareMessage = `📢 ${notice.title}\n\n${notice.description}`;

      if (notice.type === 'EVENT') {
        shareMessage += '\n\n🎯 Event Details:';

        if (notice.startDate) {
          const eventDate = format(parseISO(notice.startDate), 'MMMM dd, yyyy');
          shareMessage += `\n📅 Date: ${eventDate}`;
        }

        if (notice.startTime) {
          shareMessage += `\n🕐 Time: ${notice.startTime}`;
          if (notice.endTime) {
            shareMessage += ` - ${notice.endTime}`;
          }
        }

        if (notice.eventUrl) {
          shareMessage += `\n🔗 Join: ${notice.eventUrl}`;
        }
      }

      const result = await Share.share({
        message: shareMessage,
        title: notice.title,
      });

      if (result.action === Share.sharedAction) {
        console.log('Event shared successfully');
      }
    } catch (error) {
      console.error('Error sharing event:', error);
      Alert.alert('Error', 'Failed to share event');
    }
  };

  const renderEventDetails = () => {
    if (notice?.type !== 'EVENT') return null;

    return (
      <View style={styles.eventDetailsContainer}>
        <View style={styles.eventHeader}>
          <Ionicons name="calendar-outline" size={24} color={Colors.primary} />
          <Text style={styles.eventHeaderText}>Event Details</Text>
        </View>

        {notice.startDate && (
          <View style={styles.eventDetailRow}>
            <Ionicons name="calendar" size={20} color="#666" />
            <View style={styles.eventDetailContent}>
              <Text style={styles.eventDetailLabel}>Date</Text>
              <Text style={styles.eventDetailValue}>
                {format(parseISO(notice.startDate), 'EEEE, MMMM dd, yyyy')}
              </Text>
            </View>
          </View>
        )}

        {(notice.startTime || notice.endTime) && (
          <View style={styles.eventDetailRow}>
            <Ionicons name="time" size={20} color="#666" />
            <View style={styles.eventDetailContent}>
              <Text style={styles.eventDetailLabel}>Time</Text>
              <Text style={styles.eventDetailValue}>
                {notice.startTime}
                {notice.endTime && ` - ${notice.endTime}`}
              </Text>
            </View>
          </View>
        )}

        {notice.endDate && notice.endDate !== notice.startDate && (
          <View style={styles.eventDetailRow}>
            <Ionicons name="calendar-outline" size={20} color="#666" />
            <View style={styles.eventDetailContent}>
              <Text style={styles.eventDetailLabel}>End Date</Text>
              <Text style={styles.eventDetailValue}>
                {format(parseISO(notice.endDate), 'EEEE, MMMM dd, yyyy')}
              </Text>
            </View>
          </View>
        )}

        {notice.eventUrl && (
          <TouchableOpacity
            style={styles.eventUrlButton}
            onPress={() => handleEventUrlPress(notice.eventUrl)}>
            <Ionicons name="link" size={20} color="white" />
            <Text style={styles.eventUrlText}>Join Event</Text>
            <Ionicons name="open-outline" size={16} color="white" />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderImages = () => {
    if (!notice?.images || notice.images.length === 0) return null;

    return (
      <View style={styles.imagesContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {notice.images.map((image: any, index: number) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setSelectedImage(image.imageUrl);
                setModalVisible(true);
              }}>
              <Image source={{uri: image.imageUrl}} style={styles.image} />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!notice) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text>Notice not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {notice.type === 'EVENT' ? 'Event Details' : 'Notice Details'}
        </Text>
        <TouchableOpacity onPress={handleShareEvent}>
          <Ionicons name="share-outline" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {/* Notice Type Badge */}
        <View style={styles.typeBadgeContainer}>
          <View
            style={[
              styles.typeBadge,
              {
                backgroundColor:
                  notice.type === 'EVENT' ? '#1890ff' : '#52c41a',
              },
            ]}>
            <Ionicons
              name={notice.type === 'EVENT' ? 'calendar' : 'document-text'}
              size={16}
              color="white"
            />
            <Text style={styles.typeBadgeText}>{notice.type}</Text>
          </View>
        </View>

        {/* Title */}
        <Text style={styles.title}>{notice.title}</Text>

        {/* Description */}
        <Text style={styles.description}>{notice.description}</Text>

        {/* Images */}
        {renderImages()}

        {/* Event Details */}
        {renderEventDetails()}

        {/* Created Date */}
        <View style={styles.createdDateContainer}>
          <Text style={styles.createdDate}>
            Posted on {format(parseISO(notice.createdAt), 'MMMM dd, yyyy')}
          </Text>
        </View>
      </ScrollView>

      {/* Image Modal */}
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setModalVisible(false)}>
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>
          {selectedImage && (
            <Image source={{uri: selectedImage}} style={styles.modalImage} />
          )}
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#003580',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  typeBadgeContainer: {
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  typeBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  typeBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    lineHeight: 30,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  imagesContainer: {
    marginBottom: 20,
  },
  image: {
    width: width * 0.8,
    height: 200,
    borderRadius: 12,
    marginRight: 12,
  },
  eventDetailsContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  eventHeaderText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  eventDetailRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 12,
  },
  eventDetailContent: {
    flex: 1,
  },
  eventDetailLabel: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  eventDetailValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  eventUrlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginTop: 8,
    gap: 8,
  },
  eventUrlText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  createdDateContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  createdDate: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
  },
  modalImage: {
    width: width * 0.9,
    height: height * 0.7,
    resizeMode: 'contain',
  },
});

export default NoticeDetailScreen;
