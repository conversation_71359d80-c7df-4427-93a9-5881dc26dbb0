import {
  ActivityIndicator,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import {addSkills, getSkills, updateSkillById} from '../services/authService';
import {useSelector} from 'react-redux';
import Colors from '../constants/Colors';
import {usePermissions} from '../hooks/usePermissions';
import normalize from '../types/utiles';
import {ALERT_TYPE, Dialog} from './CustomAlert';
import Font from '../constants/Font';

interface SkillsProps {
  refRBSheet: any;
  userInfo: any;
  onSave?: any;
  fetchUserDetails: any;
  isEdit: boolean;
  userSkills: any;
}

const Skills: React.FC<SkillsProps> = ({
  refRBSheet,
  onSave,
  userInfo,
  fetchUserDetails,
  isEdit,
  userSkills,
}) => {
  const [skills, setSkills] = useState<any>([]);
  const [selectedSkillIds, setSelectedSkillIds] = useState<number[]>([]);
  const userId = useSelector((state: any) => state.auth.userId);
  const {canAddSkills} = usePermissions();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchStacksPositions = async () => {
      setLoading(true);
      try {
        const response = await getSkills();
        setSkills(response?.skills || []);
      } catch (error) {
        console.error('Error fetching skills:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchStacksPositions();
  }, []);

  useEffect(() => {
    const initialSelectedSkills = userSkills.map((skill: any) => skill.skillId);
    setSelectedSkillIds(initialSelectedSkills);
  }, [userSkills]);

  const handleSkillToggle = (skill: any) => {
    setSelectedSkillIds(prevSelectedSkillIds => {
      if (prevSelectedSkillIds.includes(skill.id)) {
        // Remove skillId if it already exists
        return prevSelectedSkillIds.filter(id => id !== skill.id);
      }
      // Add skillId if it doesn't exist
      return [...prevSelectedSkillIds, skill.id];
    });
  };

  const isSkillSelected = (skill: any) => selectedSkillIds.includes(skill.id);

  const handleAddSkill = async () => {
    // Check permission before adding skills
    if (!canAddSkills) {
      Dialog.show({
        type: ALERT_TYPE.WARNING,
        title: 'Permission Denied',
        textBody:
          "You don't have permission to add skills. Contact your administrator for access.",
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    try {
      const payload = {
        skillIds: selectedSkillIds,
      };
      let response;
      if (isEdit) {
        response = await updateSkillById(userId, payload);
      } else {
        response = await addSkills(userId, payload);
      }
      if (response?.success) {
        Dialog.show({
          type: ALERT_TYPE.INFO,
          title: 'Success',
          textBody: `Skills ${isEdit ? 'updated' : 'added'} successfully!`,
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
        fetchUserDetails();
      } else {
        Dialog.show({
          type: ALERT_TYPE.DANGER,
          title: 'Error',
          textBody: 'Failed to add skill. Please try again.',
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      }
      if (refRBSheet?.current?.close) {
        refRBSheet.current.close();
      }
    } catch (error) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Failed to add skill. Please try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      console.error('Error adding skills:', error);
    }
  };

  if (loading) {
    return (
      <View
        style={{
          height: '100%',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Select Skills</Text>
        </View>
        <View style={styles.containerSkills}>
          <ScrollView>
            <View style={styles.skillsContainer}>
              {skills.map((skill: any) => (
                <TouchableOpacity
                  key={skill.id}
                  style={[
                    styles.skillPill,
                    isSkillSelected(skill) && styles.selectedSkillPill,
                  ]}
                  onPress={() => handleSkillToggle(skill)}>
                  <Text
                    style={[
                      styles.skillText,
                      isSkillSelected(skill) && styles.selectedSkillText,
                    ]}>
                    {skill.name}
                  </Text>
                  <Text
                    style={[
                      styles.plusSign,
                      isSkillSelected(skill) && styles.selectedSkillText,
                    ]}>
                    +
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
          <TouchableOpacity style={styles.addButton} onPress={handleAddSkill}>
            <Text style={styles.addButtonText}>
              {isEdit ? 'Save' : 'Add Skill'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </ScrollView>
  );
};

export default Skills;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    color: '#FFFFFF',
    marginBottom: 12,
    backgroundColor: '#002157',
  },
  title: {
    marginTop: 12,
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
  },
  containerSkills: {
    padding: 16,
    // backgroundColor: '#F5F5F5',
    borderRadius: 8,
  },
  titleSkills: {
    fontSize: 14,
    color: '#4A4A4A',
    marginBottom: 12,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  skillPill: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#E0E0E0',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginBottom: 8,
  },
  skillText: {
    fontSize: 14,
    color: '#333333',
    marginRight: 4,
  },
  selectedSkillPill: {
    backgroundColor: '#007CBC',
  },
  selectedSkillText: {
    color: '#FFFFFF',
  },
  plusSign: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: '#007CBC',
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 4,
    alignSelf: 'flex-end',
    marginTop: 12,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
});
