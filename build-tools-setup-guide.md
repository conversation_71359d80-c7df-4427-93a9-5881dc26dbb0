# Visual Studio Build Tools Setup Guide

## What You Need to Install

### Required Components:
- ✅ **C++ build tools** workload
- ✅ **MSVC v143 - VS 2022 C++ x64/x86 build tools (Latest)**
- ✅ **Windows 11 SDK (10.0.22621.0 or latest)**
- ✅ **CMake tools for Visual Studio**

## Installation Steps

1. **Download**: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
2. **Run installer** as Administrator
3. **Select "C++ build tools" workload**
4. **Verify required components** are checked
5. **Click Install** and wait (15-30 minutes)
6. **Restart computer** when complete

## After Installation

### Verify Installation:
```powershell
# Run this script to verify everything is installed correctly
.\verify-build-tools.ps1
```

### Test React Native Build:
```powershell
# Clean previous build
cd android
.\gradlew clean
cd ..

# Clear React Native caches
npx react-native clean

# Try building again
npx react-native run-android
```

## Troubleshooting

### If C++ compiler not found:
1. **Use Developer Command Prompt**:
   - Search "Developer Command Prompt for VS 2022" in Start Menu
   - Run your React Native commands from there

2. **Manual PATH setup** (if needed):
   - Add to System PATH: `C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\[version]\bin\Hostx64\x64`

### If CMake issues persist:
- Ensure CMake is in PATH
- Try using VS Developer Command Prompt
- Verify Windows SDK is installed

### If build still fails:
1. **Clear all caches**:
   ```powershell
   Remove-Item -Recurse -Force node_modules
   npm install
   cd android && .\gradlew clean && cd ..
   npx react-native clean
   ```

2. **Check React Native Reanimated version**:
   - Consider downgrading if using latest version
   - Check compatibility with your React Native version

## Expected File Sizes
- **Download**: ~1.4 MB (vs_buildtools.exe)
- **Installation**: ~2-4 GB total
- **Time**: 15-30 minutes + restart

## Success Indicators
- ✅ `where cl` shows C++ compiler path
- ✅ `cmake --version` shows CMake version
- ✅ React Native build completes without CMake errors
- ✅ App runs on Android device/emulator
