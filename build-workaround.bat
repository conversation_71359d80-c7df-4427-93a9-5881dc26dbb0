@echo off
echo 🔧 Alumni App Build Workaround
echo =============================

echo.
echo 📱 Step 1: Temporarily removing gesture handler...
move node_modules\react-native-gesture-handler node_modules\react-native-gesture-handler.bak

echo.
echo 🔨 Step 2: Building without gesture handler...
cd android
call gradlew assembleDebug
cd ..

echo.
echo 📱 Step 3: Restoring gesture handler...
move node_modules\react-native-gesture-handler.bak node_modules\react-native-gesture-handler

echo.
echo 📦 Step 4: Installing APK...
adb install android\app\build\outputs\apk\debug\app-debug.apk

echo.
echo ✅ Build completed!
echo Note: Some gesture features may not work until gesture handler is fixed.

pause
