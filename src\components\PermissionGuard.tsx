import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {usePermissions} from '../hooks/usePermissions';
import {MobilePermission} from '../services/permissionService';

interface PermissionGuardProps {
  permission: MobilePermission;
  children: React.ReactNode;
  showMessage?: boolean;
  fallback?: React.ReactNode;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permission,
  children,
  showMessage = false,
  fallback = null,
}) => {
  const {hasPermission, isPermissionsLoaded} = usePermissions();

  // Show loading while permissions are being loaded
  if (!isPermissionsLoaded) {
    return showMessage ? (
      <View style={styles.messageContainer}>
        <Text style={styles.messageText}>Loading permissions...</Text>
      </View>
    ) : null;
  }

  // Check if user has the required permission
  if (!hasPermission(permission)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (showMessage) {
      return (
        <View style={styles.messageContainer}>
          <Text style={styles.messageText}>
            You don't have permission to {getPermissionDescription(permission)}
          </Text>
        </View>
      );
    }

    return null;
  }

  return <>{children}</>;
};

// Helper function to get user-friendly permission descriptions
const getPermissionDescription = (permission: MobilePermission): string => {
  switch (permission) {
    case 'ADD_POST':
      return 'create posts';
    case 'COMMENT_ON_POST':
      return 'comment on posts';
    case 'LIKE_POST':
      return 'like posts';
    case 'ENTER_CHAT_FORUM':
      return 'access chat forums';
    default:
      return 'access this feature';
  }
};

const styles = StyleSheet.create({
  messageContainer: {
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    margin: 8,
  },
  messageText: {
    color: '#6c757d',
    textAlign: 'center',
    fontSize: 14,
  },
});

export default PermissionGuard;
