/**
 * @format
 */

import {AppRegistry} from 'react-native';
import messaging from '@react-native-firebase/messaging';
import notifee, {EventType} from '@notifee/react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import App from './App';
import {name as appName} from './app.json';

// Helper function to increment badge count in storage
const incrementBadgeInStorage = async badgeKey => {
  try {
    const currentCount = await AsyncStorage.getItem(badgeKey);
    const newCount = currentCount ? parseInt(currentCount, 10) + 1 : 1;
    await AsyncStorage.setItem(badgeKey, newCount.toString());
    console.log(`🔔 Background: Incremented ${badgeKey} to ${newCount}`);
    return newCount;
  } catch (error) {
    console.error(`🔔 Background: Error incrementing ${badgeKey}:`, error);
    return 0;
  }
};

// Register background handler for Firebase messaging
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('🔔 Background: Message received!', remoteMessage);

  try {
    // Handle the background message here
    if (remoteMessage.data?.body) {
      const messageData = JSON.parse(remoteMessage.data.body);
      console.log('🔔 Background: Parsed message data:', messageData);

      // Increment badge counts based on notification type
      if (messageData.type === 'NOTICE') {
        // Get user ID from the message data or from stored auth data
        const userId = messageData.userId || remoteMessage.data?.userId;
        if (userId) {
          const noticeBadgeKey = `notice_badge_${userId}`;
          await incrementBadgeInStorage(noticeBadgeKey);
        }
      } else if (
        messageData.type === 'GROUP_MESSAGE' ||
        messageData.type === 'CHAT_MESSAGE'
      ) {
        // Get user ID for forum badge
        const userId = messageData.userId || remoteMessage.data?.userId;
        if (userId) {
          const forumBadgeKey = `forum_badge_${userId}`;
          await incrementBadgeInStorage(forumBadgeKey);
        }
      }

      // Display notification using notifee
      const channelId = await notifee.createChannel({
        id: 'default',
        name: 'Default Channel',
      });

      await notifee.displayNotification({
        title: remoteMessage.data.title || 'New Message',
        body: messageData.content || 'You have a new message',
        android: {
          channelId,
          smallIcon: 'ic_launcher',
          pressAction: {
            id: 'default',
          },
        },
        data: {
          body: remoteMessage.data.body,
          type: messageData.type,
          // Include relevant data based on notification type
          ...(messageData.type === 'CHAT' && {
            groupId: messageData.groupId,
            groupName: messageData.groupName,
            groupLogo: messageData.groupLogo,
          }),
          ...(messageData.type === 'NOTICE' &&
            messageData.image &&
            typeof messageData.image === 'string' && {
              image: messageData.image,
            }),
          // For POST_LIKE notifications
          ...(messageData.type === 'POST_LIKE' && {
            postId: messageData.postId?.toString() || '',
            likerName: messageData.likerName || '',
          }),
          // For POST_COMMENT notifications
          ...(messageData.type === 'POST_COMMENT' && {
            postId: messageData.postId?.toString() || '',
            commenterName: messageData.commenterName || '',
            commentText: messageData.commentText || '',
          }),
          // For NEW_POST notifications
          ...(messageData.type === 'NEW_POST' && {
            postId: messageData.postId?.toString() || '',
            authorName: messageData.authorName || '',
            authorId: messageData.authorId?.toString() || '',
          }),
        },
      });
    }
  } catch (error) {
    console.error('Error handling background message:', error);
  }
});

// Register background event handler for notifee
notifee.onBackgroundEvent(async ({type, detail}) => {
  console.log('Background Event:', type, detail);

  const {notification, pressAction} = detail;

  if (type === EventType.PRESS && pressAction?.id === 'default') {
    // Handle notification press in background
    console.log('Notification pressed in background');

    // You can store the navigation intent to handle when app opens
    if (notification?.data?.body) {
      try {
        const messageData = JSON.parse(notification.data.body);
        // Store navigation data for when app opens
        // This will be handled in the app when it becomes active
      } catch (error) {
        console.error('Error parsing notification data:', error);
      }
    }
  }
});

AppRegistry.registerComponent(appName, () => App);
