import {
  Dimensions,
  ImageBackground,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import Spacing from '../../constants/Spacing';
import FontSize from '../../constants/FontSize';
import Colors from '../../constants/Colors';
import Font from '../../constants/Font';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../types';
const {height} = Dimensions.get('window');

type Props = NativeStackScreenProps<RootStackParamList, 'Welcome'>;

const WelcomeScreen: React.FC<Props> = ({navigation: {navigate}}) => {
  return (
    <SafeAreaView>
      <View>
        <ImageBackground
          style={{
            height: height / 2.5,
            marginTop: 140,
          }}
          resizeMode="contain"
          // source={{
          //   uri: 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1737005087/welcome-img_trc61n.png',
          // }}
          source={require('../../assets/images/alumini3.png')}
        />
        <View
          style={{
            paddingHorizontal: Spacing * 4,
            paddingTop: Spacing * 2,
          }}>
          {/* <Text
            style={{
              fontSize: FontSize.xxLarge,
              color: Colors.primary,
              fontFamily: Font['poppins-bold'],
              textAlign: 'center',
              fontWeight: 800,
            }}>
            Samuel Gnanam IT Centre
          </Text> */}

          <Text
            style={{
              fontSize: FontSize.medium,
              color: Colors.text,
              fontFamily: Font['poppins-regular'],
              textAlign: 'center',
              marginTop: Spacing * 2,
              letterSpacing: 1,
            }}>
            Explore all the existing employees and friends here
          </Text>
        </View>
        <View
          style={{
            paddingHorizontal: Spacing * 2,
            paddingTop: Spacing * 6,
            // flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            display: 'flex',
          }}>
          <TouchableOpacity
            onPress={() => navigate('Login')}
            style={{
              backgroundColor: Colors.primary,
              paddingVertical: Spacing * 1,
              paddingHorizontal: Spacing * 2,
              width: '50%',
              borderRadius: Spacing,
              shadowColor: Colors.primary,
              shadowOffset: {
                width: 0,
                height: Spacing,
              },
              shadowOpacity: 0.3,
              shadowRadius: Spacing,
            }}>
            <Text
              style={{
                fontFamily: Font['poppins-bold'],
                color: Colors.onPrimary,
                fontSize: FontSize.large,
                textAlign: 'center',
                letterSpacing: 1,
              }}>
              Login
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigate('Register')}
            style={{
              paddingVertical: Spacing * 1.5,
              paddingHorizontal: Spacing * 2,
              width: '48%',
              borderRadius: Spacing,
            }}>
            <Text
              style={{
                fontFamily: Font['poppins-bold'],
                color: Colors.borderWithOpacity,
                fontSize: FontSize.large,
                textAlign: 'center',
                textDecorationLine: 'underline',
              }}>
              Register
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default WelcomeScreen;
