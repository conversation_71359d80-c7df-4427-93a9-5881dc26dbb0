import apiClient from './axios';
import apiService from './apiService';

export const fetchMessages = async (groupId: any, userId: number) => {
  console.log(`/messages/groups/${groupId}/messages/${userId}`);

  try {
    const response = await apiClient.get(
      `/messages/groups/${groupId}/messages/${userId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const fetchGroups = async () => {
  try {
    const response = await apiService.getJson('group/groups');
    console.log('fetchGroups response:', response);
    return response;
  } catch (error) {
    console.error('Error in fetchGroups:', error);
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

// Fetch groups filtered by user's batch
export const fetchGroupsByUserBatch = async (userId: string) => {
  try {
    const response = await apiService.getJson(`group/user/${userId}/groups`);
    console.log('fetchGroupsByUserBatch response:', response);
    return response;
  } catch (error) {
    console.error('Error in fetchGroupsByUserBatch:', error);
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const UnreadMessage = async (userId: string) => {
  try {
    const response = await apiClient.get(
      `/messages/unread-count-mgs/${userId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

// New function to send messages using apiService with automatic token handling
export const sendMessage = async (formData: FormData) => {
  try {
    const response = await apiService.post('messages/sent', formData);
    console.log('📡 Response received:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Response error details:', errorText);
      throw new Error(`HTTP error! Status: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Message sent successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error sending message:', error);

    // More detailed error logging
    if (error && typeof error === 'object') {
      if ('response' in error) {
        console.error('📡 Response error:', error.response);
      } else if ('request' in error) {
        console.error('📤 Request error:', error.request);
      } else if ('message' in error) {
        console.error('💬 Error message:', error.message);
      }
    }

    throw error;
  }
};

export const markMessagesAsRead = async (groupId: string, userId: string) => {
  try {
    const response = await apiClient.post('/messages/messages/read/all', {
      groupId,
      userId,
    });
    return response.data;
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

export const GroupService = {
  Groupcheck: async (userId: string, groupId: string) => {
    try {
      const response = await apiClient.get(
        `/group-member/group-members/${userId}/${groupId}`,
      );
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        console.error((error as any).response?.data || error.message);
      } else {
        console.error(error);
      }
      throw error;
    }
  },

  // Check if user is blocked in a group
  checkBlockStatus: async (userId: string, groupId: string) => {
    try {
      const response = await apiClient.get(`/group/${groupId}/members`);
      const members = response.data.members || [];
      const userMember = members.find(
        (member: any) => member.userId === userId,
      );
      return {
        isBlocked: userMember?.isBlocked || false,
        blockedAt: userMember?.blockedAt,
        blockedBy: userMember?.blockedBy,
      };
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        console.error((error as any).response?.data || error.message);
      } else {
        console.error(error);
      }
      throw error;
    }
  },

  // Get group members
  getGroupMembers: async (groupId: string) => {
    try {
      const response = await apiClient.get(`/group/${groupId}/members`);
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        console.error((error as any).response?.data || error.message);
      } else {
        console.error(error);
      }
      throw error;
    }
  },
  joinGroup: async (userId: string, groupId: string) => {
    try {
      const response = await apiClient.post(`/group-member/create`, {
        userId,
        groupId,
      });
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        console.error((error as any).response?.data || error.message);
      } else {
        console.error(error);
      }
      throw error;
    }
  },

  // Check if user is blocked in a group
  checkUserBlockStatus: async (userId: string, groupId: string) => {
    try {
      const response = await apiClient.get(
        `/group-member/block-status/${userId}/${groupId}`,
      );
      return response.data;
    } catch (error) {
      if (error instanceof Error && 'response' in error) {
        console.error((error as any).response?.data || error.message);
      } else {
        console.error(error);
      }
      throw error;
    }
  },
};

export const leaveGroupService = async (groupId: string, userId: string) => {
  try {
    const response = await apiClient.delete(
      `/group-member/delete/${userId}/${groupId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );

    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const editGroupChat = async (updatedMessage: any) => {
  try {
    const response = await apiClient.put(
      `/messages/messages/${updatedMessage.messageId}`,
      {
        content: updatedMessage.content,
      },
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const deleteGroupChat = async (messageId: number) => {
  try {
    const response = await apiClient.delete(
      `/messages/messages/user/${messageId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};
