# API Service Usage Guide

This guide shows how to use the new `apiService` that automatically handles authentication tokens for all API calls.

## Features

- **Automatic Token Management**: Automatically adds Bearer tokens to all requests
- **Token Refresh**: Automatically refreshes expired tokens and retries failed requests
- **Error Handling**: Comprehensive error handling with proper HTTP status codes
- **Multiple Request Types**: Support for GET, POST, PUT, DELETE, PATCH requests
- **File Uploads**: Special handling for FormData and file uploads
- **TypeScript Support**: Full TypeScript support with proper typing

## Basic Usage

### Import the Service

```typescript
import apiService from '../services/apiService';
```

### GET Requests

```typescript
// Simple GET request
const response = await apiService.get('users/profile');
const data = await response.json();

// GET with JSON response utility
const userData = await apiService.getJson('users/profile');

// GET without authentication (for public endpoints)
const publicData = await apiService.getJson('public/data', { requiresAuth: false });
```

### POST Requests

```typescript
// POST with JSON data
const newUser = {
  name: '<PERSON>e',
  email: '<EMAIL>'
};
const response = await apiService.post('users/create', newUser);
const result = await response.json();

// POST with JSON response utility
const result = await apiService.postJson('users/create', newUser);
```

### File Uploads

```typescript
// Upload files using FormData
const formData = new FormData();
formData.append('file', {
  uri: 'file://path/to/image.jpg',
  type: 'image/jpeg',
  name: 'image.jpg'
});
formData.append('userId', '123');

// Using the upload utility method
const uploadResult = await apiService.uploadFile('files/upload', formData);

// Or using the generic post method
const response = await apiService.post('files/upload', formData);
const result = await response.json();
```

### PUT/PATCH Requests

```typescript
// Update user data
const updatedData = { name: 'Jane Doe' };
const result = await apiService.putJson('users/123', updatedData);

// Partial update with PATCH
const partialUpdate = { status: 'active' };
const result = await apiService.patch('users/123/status', partialUpdate);
```

### DELETE Requests

```typescript
// Delete a resource
const response = await apiService.delete('users/123');

// Delete with JSON response
const result = await apiService.deleteJson('users/123');
```

## Error Handling

The API service automatically handles common errors:

```typescript
try {
  const data = await apiService.getJson('protected/data');
  console.log('Success:', data);
} catch (error) {
  console.error('API Error:', error.message);
  // Handle specific error cases
  if (error.message.includes('401')) {
    // Token refresh failed, redirect to login
    // navigation.navigate('Login');
  }
}
```

## Migration from Raw Fetch

### Before (Raw Fetch)
```typescript
const token = await AsyncStorage.getItem('accessToken');
const response = await fetch(`${baseURL}messages/sent`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(data)
});

if (!response.ok) {
  throw new Error(`HTTP error! status: ${response.status}`);
}
const result = await response.json();
```

### After (API Service)
```typescript
const result = await apiService.postJson('messages/sent', data);
```

## Migration from Axios

### Before (Axios with apiClient)
```typescript
const response = await apiClient.post('/posts/create', formData, {
  headers: {
    'Accept': 'application/json',
    'Content-Type': 'multipart/form-data',
  },
});
return response.data;
```

### After (API Service)
```typescript
return await apiService.uploadFile('posts/create', formData);
```

## Custom Configuration

You can create custom instances for different base URLs:

```typescript
import { ApiService } from '../services/apiService';

const customApiService = new ApiService('https://api.example.com/v2/');
const data = await customApiService.getJson('custom/endpoint');
```

## Best Practices

1. **Use the utility methods** (`getJson`, `postJson`, etc.) for simpler code
2. **Handle errors appropriately** in your components
3. **Use the `requiresAuth: false` option** for public endpoints
4. **Prefer the API service over raw fetch** for consistency
5. **Keep the existing axios-based services** for complex interceptor logic

## Token Management

The service automatically:
- Retrieves tokens from AsyncStorage
- Adds Authorization headers
- Refreshes expired tokens
- Retries failed requests after token refresh
- Clears tokens on refresh failure

No manual token management is required in your components!
