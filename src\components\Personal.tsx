import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import {addPersonDetails} from '../services/authService';
import {useSelector} from 'react-redux';
import normalize from '../types/utiles';
import {ALERT_TYPE, Dialog} from './CustomAlert';

interface PersonalFormData {
  name: string;
  email: string;
  nic: string;
  phoneNo: string;
  batchNo: string;
  address: string;
}

interface PersonalProps {
  refRBSheet: any;
  userInfo: any;
  onSavePersonal: (dataPersonal: PersonalFormData) => void;
  fetchUserDetails: any;
}

const Personal: React.FC<PersonalProps> = ({
  refRBSheet,
  onSavePersonal,
  userInfo,
  fetchUserDetails,
}) => {
  const [formDataPersonal, setFormDataPersonal] = useState<PersonalFormData>({
    name: userInfo?.name,
    email: userInfo?.email,
    nic: userInfo?.nic,
    phoneNo: userInfo?.contactNo,
    batchNo: userInfo?.batchNo,
    address: userInfo?.address,
  });
  const userId = useSelector((state: any) => state.auth.userId);

  const handleSave = async () => {
    try {
      const payload = {
        name: formDataPersonal?.name,
        email: formDataPersonal?.email,
        nic: formDataPersonal?.nic,
        phoneNo: formDataPersonal?.phoneNo,
        address: formDataPersonal?.address,
      };
      const response = await addPersonDetails(userId, payload);
      fetchUserDetails();
      Dialog.show({
        type: ALERT_TYPE.INFO,
        title: 'Success',
        textBody: 'Your Details updated successfully.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      if (onSavePersonal) {
        onSavePersonal(formDataPersonal);
      }

      if (refRBSheet?.current?.close) {
        refRBSheet.current.close();
      }
    } catch (error) {
      console.error('Failed to save personal details:', error);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Personal Information</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. John Doe"
              placeholderTextColor="#999"
              value={formDataPersonal.name}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, name: text})
              }
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. <EMAIL>"
              placeholderTextColor="#999"
              value={formDataPersonal.email}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, email: text})
              }
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>NIC</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. 123456789V"
              placeholderTextColor="#999"
              value={formDataPersonal.nic}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, nic: text})
              }
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Phone No</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. 0712345678"
              placeholderTextColor="#999"
              value={formDataPersonal.phoneNo}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, phoneNo: text})
              }
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Batch No</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. Batch 15"
              placeholderTextColor="#999"
              value={formDataPersonal.batchNo}
              editable={false}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, batchNo: text})
              }
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Address</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. 123 Main St, City, Country"
              placeholderTextColor="#999"
              value={formDataPersonal.address}
              onChangeText={text =>
                setFormDataPersonal({...formDataPersonal, address: text})
              }
            />
          </View>
        </View>

        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>Save</Text>
        </TouchableOpacity>
      </SafeAreaView>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 24,
  },
  title: {
    fontSize: normalize(20),
    fontWeight: '600',
    marginLeft: 50,
    color: '#000000',
  },
  form: {
    paddingHorizontal: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#000000',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#333',
  },
  saveButton: {
    backgroundColor: '#007CBC',
    marginHorizontal: 16,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Personal;
