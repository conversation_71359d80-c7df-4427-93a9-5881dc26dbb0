import {
  ActivityIndicator,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  StatusBar,
} from 'react-native';
import React, {useEffect, useState} from 'react';
// import Spacing from '../../constants/Spacing';
// import FontSize from '../../constants/FontSize';
// import Colors from '../../constants/Colors';
import Font from '../../constants/Font';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../types';
import AppTextInput from '../../components/AppTextInput';
import normalize from '../../types/utiles';
import {
  createUserRegister,
  getStacksPositions,
} from '../../services/authService';
import DropDownPicker from 'react-native-dropdown-picker';
import {ALERT_TYPE, Dialog} from '../../components/CustomAlert';
import Ionicons from 'react-native-vector-icons/Ionicons';

type Props = NativeStackScreenProps<RootStackParamList, 'Register'>;

const RegisterScreen: React.FC<Props> = ({navigation: {navigate}}) => {
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [nic, setNIC] = useState<string>('');
  const [contactNo, setContactNo] = useState<string>('');
  const [batchno, setBatchno] = useState<string>('');
  const [batches, setBatches] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isTrainee, setIsTrainee] = useState<boolean>(true); // Default checked
  const [guestBatchId, setGuestBatchId] = useState<string>('');

  useEffect(() => {
    const fetchBatches = async () => {
      try {
        const response = await getStacksPositions();

        // Filter batches based on trainee status
        let filteredBatches = response.data.batch;
        if (isTrainee) {
          // Show only STUDENT_BATCH type batches for trainees
          filteredBatches = response.data.batch.filter(
            (batch: {type: string}) => batch.type === 'STUDENT_BATCH',
          );
        }

        const formattedBatches = filteredBatches.map(
          (batch: {id: number; batchNo: string; type: string}) => ({
            label: batch.batchNo,
            value: batch.id,
          }),
        );
        setBatches(formattedBatches);

        // Find and store GUEST batch ID for non-trainees
        const guestBatch = response.data.batch.find(
          (batch: {type: string}) => batch.type === 'GUEST',
        );
        if (guestBatch) {
          setGuestBatchId(guestBatch.id.toString());
        }
      } catch (error) {
        console.log(error);
      }
    };
    fetchBatches();
  }, [isTrainee]); // Re-fetch when trainee status changes

  const handleRegister = async () => {
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const phonePattern = /^[0-9]{10}$/;
    const nicPattern = /^[0-9]{9}[vVxX]$|^[0-9]{12}$/;

    // Validate required fields - batch is only required for trainees
    const requiredFieldsCheck = isTrainee
      ? !firstName || !lastName || !email || !nic || !contactNo || !batchno
      : !firstName || !lastName || !email || !nic || !contactNo;

    if (requiredFieldsCheck) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please fill all required fields',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }
    if (!emailPattern.test(email)) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please enter a valid email address.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (!phonePattern.test(contactNo)) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please enter a valid 10-digit contact number.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (!nicPattern.test(nic)) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Please enter a valid NIC number.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }
    setLoading(true);
    try {
      const payload = {
        firstName: firstName,
        lastName: lastName,
        email: email,
        phoneNo: contactNo,
        nic: nic,
        batchId: isTrainee ? batchno : guestBatchId, // Use selected batch for trainees, GUEST batch for non-trainees
        address: null,
        timeline: undefined,
        type: 'MOBILE',
      };
      const response = await createUserRegister(payload);
      Dialog.show({
        type: ALERT_TYPE.INFO,
        title: 'Success',
        textBody: response.message,
        // 'Account created successfully Wait for the Admin Verification',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      navigate('Login');
      // } else {
      //   Dialog.show({
      //     type: ALERT_TYPE.DANGER,
      //     title: 'Error',
      //     textBody: response.data.message || 'Registration failed',
      //     button: 'OK',
      //     onPressButton: () => Dialog.hide(),
      //   });
      // }
    } catch (error) {
      console.log(error);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Something went wrong. Please try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#002157" />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}>
          {/* Header with Logo */}
          <View style={styles.header}>
            {/* <View style={styles.logoContainer}>
              <Image
                source={require('../../assets/images/LOGO.jpg')}
                style={styles.logo}
                resizeMode="contain"
              />
            </View> */}
            <Text style={styles.title}>Create Account</Text>
            <Text style={styles.subtitle}>
              Join our alumni community and stay connected
            </Text>
          </View>

          {/* Form Container */}
          <View style={styles.formContainer}>
            {/* First Name Input */}
            <View style={styles.inputContainer}>
              <Ionicons
                name="person-outline"
                size={20}
                color="#3795BD"
                style={styles.inputIcon}
              />
              <AppTextInput
                placeholder="First Name"
                value={firstName}
                onChangeText={setFirstName}
                style={styles.textInput}
                placeholderTextColor="#999"
              />
            </View>

            {/* Last Name Input */}
            <View style={styles.inputContainer}>
              <Ionicons
                name="person-outline"
                size={20}
                color="#3795BD"
                style={styles.inputIcon}
              />
              <AppTextInput
                placeholder="Last Name"
                value={lastName}
                onChangeText={setLastName}
                style={styles.textInput}
                placeholderTextColor="#999"
              />
            </View>
            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Ionicons
                name="mail-outline"
                size={20}
                color="#3795BD"
                style={styles.inputIcon}
              />
              <AppTextInput
                placeholder="Email Address"
                value={email}
                onChangeText={setEmail}
                style={styles.textInput}
                placeholderTextColor="#999"
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            {/* NIC Input */}
            <View style={styles.inputContainer}>
              <Ionicons
                name="card-outline"
                size={20}
                color="#3795BD"
                style={styles.inputIcon}
              />
              <AppTextInput
                placeholder="NIC Number"
                value={nic}
                onChangeText={setNIC}
                style={styles.textInput}
                placeholderTextColor="#999"
              />
            </View>

            {/* Contact Number Input */}
            <View style={styles.inputContainer}>
              <Ionicons
                name="call-outline"
                size={20}
                color="#3795BD"
                style={styles.inputIcon}
              />
              <AppTextInput
                placeholder="Contact Number"
                value={contactNo}
                onChangeText={setContactNo}
                style={styles.textInput}
                placeholderTextColor="#999"
                keyboardType="numeric"
                maxLength={10}
              />
            </View>

            {/* Trainee Checkbox */}
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={styles.checkboxRow}
                onPress={() => {
                  setIsTrainee(!isTrainee);
                  // Clear batch selection when unchecking trainee
                  if (isTrainee) {
                    setBatchno('');
                  }
                }}>
                <View style={styles.checkbox}>
                  <Ionicons
                    name={isTrainee ? 'checkbox' : 'square-outline'}
                    size={24}
                    color={isTrainee ? '#3795BD' : '#999'}
                  />
                </View>
                <Text style={styles.checkboxLabel}>Are you a trainee?</Text>
              </TouchableOpacity>
            </View>

            {/* Batch Dropdown - Only show for trainees */}
            {isTrainee && (
              <View style={styles.dropdownContainer}>
                <Ionicons
                  name="school-outline"
                  size={20}
                  color="#3795BD"
                  style={styles.dropdownIcon}
                />
                <DropDownPicker
                  open={open}
                  value={batchno}
                  items={batches}
                  setOpen={setOpen}
                  setValue={setBatchno}
                  setItems={setBatches}
                  placeholder="Select Batch No"
                  listMode="MODAL"
                  modalProps={{
                    animationType: 'slide',
                  }}
                  modalContentContainerStyle={styles.modalContainer}
                  containerStyle={styles.pickerContainer}
                  style={styles.picker}
                  dropDownContainerStyle={styles.dropdownList}
                  textStyle={styles.pickerText}
                  placeholderStyle={styles.pickerPlaceholder}
                  scrollViewProps={{
                    nestedScrollEnabled: true,
                  }}
                  flatListProps={{
                    nestedScrollEnabled: true,
                  }}
                  onOpen={() => Keyboard.dismiss()}
                />
              </View>
            )}
          </View>

          {/* Register Button */}
          <TouchableOpacity
            onPress={handleRegister}
            disabled={loading}
            style={[styles.registerButton, loading && styles.disabledButton]}>
            {loading ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Ionicons
                  name="person-add"
                  size={20}
                  color="#fff"
                  style={styles.buttonIcon}
                />
                <Text style={styles.registerButtonText}>Create Account</Text>
              </>
            )}
          </TouchableOpacity>

          {/* Login Link */}
          <TouchableOpacity
            onPress={() => navigate('Login')}
            style={styles.loginLink}>
            <Text style={styles.loginLinkText}>
              Already have an account?
              <Text style={styles.loginLinkBold}> Sign In</Text>
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: normalize(20),
  },
  header: {
    alignItems: 'center',
    paddingTop: normalize(40),
    paddingBottom: normalize(30),
  },
  logoContainer: {
    width: normalize(100),
    height: normalize(100),
    borderRadius: normalize(50),
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalize(20),
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  logo: {
    width: normalize(80),
    height: normalize(80),
    borderRadius: normalize(40),
  },
  title: {
    fontSize: normalize(28),
    fontWeight: '700',
    color: '#1a1a1a',
    fontFamily: Font['poppins-bold'],
    marginBottom: normalize(8),
    textAlign: 'center',
  },
  subtitle: {
    fontSize: normalize(16),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
    lineHeight: normalize(22),
    paddingHorizontal: normalize(20),
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: normalize(20),
    padding: normalize(15),
    marginBottom: normalize(20),
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: normalize(12),
    paddingHorizontal: normalize(15),
    marginBottom: normalize(16),
    borderWidth: 1,
    borderColor: '#e9ecef',
    height: normalize(55),
  },
  inputIcon: {
    marginRight: normalize(12),
  },
  textInput: {
    flex: 1,
    fontSize: normalize(16),
    color: '#333',
    fontFamily: Font['poppins-regular'],
    paddingVertical: 0,
  },
  dropdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: normalize(12),
    paddingLeft: normalize(15),
    marginBottom: normalize(16),
    borderWidth: 1,
    borderColor: '#e9ecef',
    height: normalize(55),
  },
  dropdownIcon: {
    marginRight: normalize(12),
  },
  pickerContainer: {
    flex: 1,
    height: '100%',
  },
  picker: {
    backgroundColor: 'transparent',
    borderWidth: 0,
    borderRadius: 0,
    paddingLeft: 0,
    minHeight: normalize(53),
  },
  pickerText: {
    fontSize: normalize(16),
    color: '#333',
    fontFamily: Font['poppins-regular'],
  },
  pickerPlaceholder: {
    fontSize: normalize(16),
    color: '#999',
    fontFamily: Font['poppins-regular'],
  },
  modalContainer: {
    backgroundColor: '#fff',
  },
  dropdownList: {
    backgroundColor: '#fff',
    maxHeight: 200,
    borderColor: '#e9ecef',
  },
  registerButton: {
    backgroundColor: '#3795BD',
    borderRadius: normalize(12),
    paddingVertical: normalize(16),
    paddingHorizontal: normalize(20),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: normalize(10),
    elevation: 3,
    shadowColor: '#3795BD',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  disabledButton: {
    backgroundColor: '#ccc',
    elevation: 0,
    shadowOpacity: 0,
  },
  buttonIcon: {
    marginRight: normalize(8),
  },
  registerButtonText: {
    color: '#fff',
    fontSize: normalize(18),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  loginLink: {
    paddingVertical: normalize(20),
    alignItems: 'center',
  },
  loginLinkText: {
    fontSize: normalize(16),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
  },
  loginLinkBold: {
    color: '#3795BD',
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
  checkboxContainer: {
    marginBottom: normalize(20),
    paddingHorizontal: normalize(5),
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: normalize(10),
  },
  checkbox: {
    marginRight: normalize(12),
  },
  checkboxLabel: {
    fontSize: normalize(16),
    color: '#333',
    fontFamily: Font['poppins-regular'],
    flex: 1,
  },
});

export default RegisterScreen;
