@echo off
echo 🧪 Testing Android Build Dependencies
echo ====================================

echo.
echo 📦 Step 1: Checking Gradle dependencies...
cd android
call gradlew dependencies --configuration debugRuntimeClasspath | findstr notifee

echo.
echo 🔍 Step 2: Checking if Notifee core is available...
call gradlew app:dependencies --configuration debugRuntimeClasspath | findstr "app.notifee:core"

echo.
echo 🔧 Step 3: Refreshing dependencies...
call gradlew --refresh-dependencies

echo.
echo ✅ Dependency check completed!
echo If you see notifee dependencies listed above, the issue should be resolved.

cd ..
pause
