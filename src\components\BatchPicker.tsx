import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import normalize from '../types/utiles';

interface BatchPickerProps {
  selectedValue: string;
  onValueChange: (value: string) => void;
  batches: Array<{ label: string; value: number }>;
  placeholder?: string;
}

const BatchPicker: React.FC<BatchPickerProps> = ({
  selectedValue,
  onValueChange,
  batches,
  placeholder = "Select Batch No"
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.pickerContainer}>
        <Picker
          selectedValue={selectedValue}
          onValueChange={onValueChange}
          style={styles.picker}
          itemStyle={styles.pickerItem}
        >
          <Picker.Item 
            label={placeholder} 
            value="" 
            enabled={false}
            style={styles.placeholderItem}
          />
          {batches.map((batch) => (
            <Picker.Item
              key={batch.value}
              label={batch.label}
              value={batch.value.toString()}
              style={styles.pickerItem}
            />
          ))}
        </Picker>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3795BD',
    backgroundColor: '#fff',
    borderRadius: normalize(5),
    height: normalize(50),
    marginBottom: normalize(15),
  },
  pickerContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  picker: {
    flex: 1,
    color: '#000',
    fontSize: normalize(14),
  },
  pickerItem: {
    fontSize: normalize(14),
    color: '#000',
  },
  placeholderItem: {
    fontSize: normalize(14),
    color: '#999',
  },
});

export default BatchPicker;
