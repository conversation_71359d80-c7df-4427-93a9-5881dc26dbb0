package com.sgicalumni

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "sgicalumni"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(this, mainComponentName, fabricEnabled)

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    handleIntent(intent)
  }

  override fun onNewIntent(intent: Intent?) {
    super.onNewIntent(intent)
    setIntent(intent)
    intent?.let { handleIntent(it) }
  }

  private fun handleIntent(intent: Intent) {
    val action = intent.action
    val data = intent.data

    Log.d("MainActivity", "Intent action: $action")
    Log.d("MainActivity", "Intent data: $data")

    if (Intent.ACTION_VIEW == action && data != null) {
      Log.d("MainActivity", "Deep link received: $data")
      Log.d("MainActivity", "Scheme: ${data.scheme}")
      Log.d("MainActivity", "Host: ${data.host}")
      Log.d("MainActivity", "Path: ${data.path}")
      Log.d("MainActivity", "Query: ${data.query}")
    }
  }
}
