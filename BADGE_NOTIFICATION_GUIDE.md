# Red Dot Notification System Guide

This guide explains how the red dot notification system works in the mobile app for showing unread notices and forum messages.

## 🎯 **What It Does**

The red dot system shows small red dots on bottom tab bar icons when:
- **New notices** are received → Red dot appears on "Notices" tab icon
- **New forum messages** are received → Red dot appears on "Forum" tab icon

The red dots automatically disappear when you visit those screens.

## 🔧 **How It Works**

### **1. Real-time Updates**
- ✅ **Socket Integration**: Listens for real-time notifications via WebSocket
- ✅ **Automatic Badges**: Shows badges immediately when new content arrives
- ✅ **Smart Filtering**: Only shows badges for messages from other users (not your own)

### **2. Auto-Clear Functionality**
- ✅ **Visit Notices Tab**: Badge clears when you tap the Notices tab
- ✅ **Visit Forum Tab**: Badge clears when you tap the Forum tab
- ✅ **Enter Chat**: Badge clears when you enter any chat room

### **3. Persistent Storage**
- ✅ **Survives App Restart**: Badge counts are maintained across app sessions
- ✅ **User-Specific**: Each user has their own badge counts

## 📱 **User Experience**

### **When New Notice Arrives:**
1. Small red dot appears on "Notices" tab icon
2. Red dot stays visible until user taps the Notices tab
3. Red dot disappears when NoticesScreen is focused

### **When New Forum Message Arrives:**
1. Small red dot appears on "Forum" tab icon
2. Red dot stays visible until user visits Forum or enters any chat
3. Red dot disappears when GroupForumScreen or ChatForumScreen is focused

### **Red Dot Display:**
- **Has Notifications**: Shows small red dot in top-right corner of icon
- **No Notifications**: No red dot visible
- **Simple & Clean**: Just a small red dot, no numbers or text

## 🔌 **Socket Events**

The system listens for these WebSocket events:

```typescript
// New notice published
socket.on('newNotice', (data) => {
  // Increments notice badge count
});

// New message in forum/group
socket.on('newMessage', (data) => {
  // Increments forum badge count (if not from current user)
});

socket.on('newGroupMessage', (data) => {
  // Increments forum badge count (if not from current user)
});

socket.on('chatMessage', (data) => {
  // Alternative event for group messages
});
```

## 🛠️ **Implementation Details**

### **1. BadgeContext**
Manages global badge state across the app:

```typescript
const { 
  noticeBadgeCount,     // Current notice badge count
  forumBadgeCount,      // Current forum badge count
  clearNoticeBadge,     // Clear notice badge
  clearForumBadge,      // Clear forum badge
  incrementNoticeBadge, // Add to notice badge
  incrementForumBadge   // Add to forum badge
} = useBadge();
```

### **2. TabBarIcon Component**
Custom tab bar icon with red dot support:

```typescript
<TabBarIcon
  focused={focused}
  iconSource={require('../assets/images/icon.png')}
  hasNotification={noticeBadgeCount > 0}  // Shows red dot when true
/>
```

### **3. Auto-Clear Integration**
Screens automatically clear badges when focused:

```typescript
// In NoticesScreen
useFocusEffect(
  useCallback(() => {
    clearNoticeBadge(); // Clear when screen is focused
  }, [clearNoticeBadge])
);
```

## 🧪 **Testing the System**

### **Method 1: Use Test Component**
Add `BadgeTestComponent` to any screen to manually test:


### **Method 2: Real Testing**
1. **Two Devices**: Use two different user accounts
2. **Create Notice**: Admin creates a new notice
3. **Send Message**: Send message in group chat
4. **Check Badges**: Other user should see badges appear
5. **Visit Screens**: Badges should disappear when visited

### **Method 3: Console Logs**
Check console for badge-related logs:
```
🔔 New notice received: {...}
🔔 New forum message received: {...}
🔔 NoticesScreen focused - clearing notice badge
🔔 GroupForumScreen focused - clearing forum badge
```

## 📋 **Current Integration Status**

### **✅ Implemented:**
- ✅ BadgeContext for state management
- ✅ TabBarIcon component with badge display
- ✅ Socket event listeners for real-time updates
- ✅ Auto-clear on Notices tab focus
- ✅ Auto-clear on Forum tab focus
- ✅ Auto-clear when entering chat rooms
- ✅ Badge count persistence across app restarts

### **🎯 Badge Locations:**
- ✅ **Notices Tab**: Shows notice badge count
- ✅ **Forum Tab**: Shows forum message badge count

### **🔄 Auto-Clear Triggers:**
- ✅ **NoticesScreen**: Clears notice badge when focused
- ✅ **GroupForumScreen**: Clears forum badge when focused
- ✅ **ChatForumScreen**: Clears forum badge when focused

## 🎉 **Benefits**

- 🔔 **Never Miss Updates**: Users see when new content arrives
- 🎯 **Smart Clearing**: Badges disappear when content is viewed
- 🚀 **Real-time**: Instant notifications via WebSocket
- 💾 **Persistent**: Badge counts survive app restarts
- 🎨 **Professional**: Clean, WhatsApp-style badge design
- 📱 **User-Friendly**: Intuitive behavior users expect

## 🔧 **Customization**

### **Change Badge Colors:**
Edit `TabBarIcon.tsx` styles:
```typescript
badge: {
  backgroundColor: '#FF3B30', // Change badge color
  borderColor: '#FFFFFF',     // Change border color
}
```

### **Add More Badge Types:**
1. Add new badge count to `BadgeContext`
2. Add socket listeners for new events
3. Update tab icons to show new badge
4. Add auto-clear to relevant screens

The badge notification system is now fully functional and ready to keep users informed of new content! 🎯
