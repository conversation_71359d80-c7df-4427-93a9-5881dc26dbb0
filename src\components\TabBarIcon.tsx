import React from 'react';
import {View, StyleSheet, Image} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface TabBarIconProps {
  focused: boolean;
  iconSource: any;
  hasNotification?: boolean;
  iconStyle?: any;
  type?: string;
}

const TabBarIcon: React.FC<TabBarIconProps> = ({
  focused,
  iconSource,
  hasNotification = false,
  iconStyle = {width: 30, height: 30},
  type,
}) => {
  // Debug: Log when hasNotification changes
  React.useEffect(() => {
    if (type === 'notice') {
      console.log('🔔 TABBAR ICON - Notice hasNotification:', hasNotification);
    }
    if (type === 'forum') {
      console.log('🔔 TABBAR ICON - Forum hasNotification:', hasNotification);
    }
  }, [hasNotification, type]);

  return (
    <View style={styles.container}>
      <View style={[styles.iconWrapper, focused && styles.focusedIconWrapper]}>
        {type === 'notice' ? (
          focused ? (
            <Image
              source={require('../assets/images/icon.png')}
              style={{width: 30, height: 30}}
              resizeMode="contain"
            />
          ) : (
            <Image
              source={require('../assets/images/group.png')}
              style={{width: 30, height: 30}}
              resizeMode="contain"
            />
          )
        ) : type === 'forum' ? (
          focused ? (
            <Ionicons name="people-outline" size={25} color={'#1759c4'} />
          ) : (
            <Ionicons name="people-outline" size={25} color={'gray'} />
          )
        ) : null}

        {hasNotification && <View style={styles.redDot} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  redDot: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 8,
    height: 8,
    backgroundColor: '#FF3B30',
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  iconWrapper: {
    padding: 4,
    borderRadius: 10, // Makes it round
    alignItems: 'center',
    justifyContent: 'center',
  },
  focusedIconWrapper: {
    backgroundColor: '#cbcaf7ff', // WhatsApp secondary green
  },
});

export default TabBarIcon;
