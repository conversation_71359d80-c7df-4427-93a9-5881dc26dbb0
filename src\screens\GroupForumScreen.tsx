import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  SafeAreaView,
  Image,
  Alert,
  Modal,
  ActivityIndicator,
} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {
  fetchGroups,
  fetchGroupsByUserBatch,
  GroupService,
  markMessagesAsRead,
  UnreadMessage,
} from '../services/chatservice';
import normalize from '../types/utiles';
import {useSelector} from 'react-redux';
import {socket} from '../services/socket';
import {usePermissions} from '../hooks/usePermissions';
import {useBadge} from '../contexts/BadgeContext';
interface Group {
  image: string | undefined;
  id: string;
  name: string;
  joined: boolean;
  unreadCount: number;
  lastMessageTime?: string;
  lastMessage?: string;
  username: string;
  isCurrentUser: boolean;
}
const GroupForumScreen: React.FC = () => {
  const navigation = useNavigation();
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const userId = useSelector((state: any) => state.auth.userId);
  const {canEnterChatForum} = usePermissions();
  const {clearForumBadge} = useBadge();

  // Helper function to format last message based on message type
  const formatLastMessage = (message: any) => {
    console.log('🚀 Formatting last message:', JSON.stringify(message));

    if (!message) return 'No messages yet';

    // Check for voice message indicators (multiple ways to detect)
    if (
      message.messageType === 'voice' ||
      message.voiceUrl ||
      message.voiceDuration ||
      (message.content === '' && message.id && !message.content?.trim())
    ) {
      console.log('✅ Detected voice message');
      return '🎤 Voice message';
    } else if (message.content && message.content.trim()) {
      console.log('✅ Detected text message:', message.content);
      return message.content;
    } else {
      console.log('⚠️ No valid message content found');
      return 'No messages yet';
    }
  };

  // Clear forum badge when screen is focused
  useFocusEffect(
    useCallback(() => {
      console.log('🔔 GroupForumScreen focused - clearing forum badge');
      clearForumBadge();
    }, [clearForumBadge]),
  );

  const getGroupsWithUnreadCounts = async () => {
    try {
      // Use batch-filtered groups instead of all groups
      const data = await fetchGroupsByUserBatch(userId.toString());
      const unreadData = await UnreadMessage(userId);
      console.log('Groups API response:', data);

      // Check if the response has the expected structure
      if (!data || !data.groups) {
        console.error('Invalid response structure:', data);
        setError('Invalid response from server');
        return;
      }

      // The new API already includes group details, so we can use them directly
      const groupsWithUnreadCounts = data.groups.map((group: Group) => {
        const unreadCountObj = unreadData.data.find(
          // @ts-ignore
          (item: {groupId: number; unreadCount: number}) =>
            item.groupId === group.id,
        );

        return {
          ...group,
          // Use unread count from API if available, otherwise from unread data
          unreadCount:
            group.unreadCount ||
            (unreadCountObj ? unreadCountObj.unreadCount : 0),
          // Use last message info from the new API
          lastMessage: formatLastMessage(
            unreadCountObj?.message || {content: group.lastMessage},
          ),
          lastMessageTime: group.lastMessageTime
            ? new Date(group.lastMessageTime).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })
            : unreadCountObj?.message?.createdAt
            ? new Date(unreadCountObj.message.createdAt).toLocaleTimeString(
                [],
                {hour: '2-digit', minute: '2-digit'},
              )
            : '',
          username:
            group.username ||
            unreadCountObj?.message?.user?.username ||
            'Unknown',
          isCurrentUser:
            group.isCurrentUser || unreadCountObj?.message?.userId === userId,
        };
      });
      setGroups(groupsWithUnreadCounts);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching batch-filtered groups:', err);
      console.error('Error details:', JSON.stringify(err, null, 2));
      console.log('Falling back to all groups...');

      // Fallback to original method if batch-filtered groups fail
      try {
        const data = await fetchGroups();
        console.log(data);

        const unreadData = await UnreadMessage(userId);
        const groupsWithUnreadCounts = data.groups.map((group: Group) => {
          const unreadCountObj = unreadData.data.find(
            // @ts-ignore
            (item: {groupId: number; unreadCount: number}) =>
              item.groupId === group.id,
          );
          return {
            ...group,
            unreadCount: unreadCountObj ? unreadCountObj.unreadCount : 0,
            isCurrentUser: unreadCountObj?.message?.userId === userId,
            lastMessage: formatLastMessage(unreadCountObj?.message),
            lastMessageTime: unreadCountObj?.message?.createdAt
              ? new Date(unreadCountObj.message.createdAt).toLocaleTimeString(
                  [],
                  {hour: '2-digit', minute: '2-digit'},
                )
              : '',
            username: unreadCountObj?.message?.user?.username || 'Unknown',
          };
        });
        setGroups(groupsWithUnreadCounts);
        setLoading(false);
      } catch (fallbackErr) {
        setError('Failed to load groups');
        setLoading(false);
      }
    }
  };

  useFocusEffect(
    useCallback(() => {
      setLoading(true);
      getGroupsWithUnreadCounts();
    }, [userId]),
  );

  useEffect(() => {
    if (socket.connected) {
      onConnect();
    }
    function onConnect() {
      console.log('chatMessage');
      socket.emit(`chatMessage`, `${userId}`);
    }

    function onDisconnect() {
      console.log('Socket disconnected');
    }

    socket.on('connect', onConnect);
    socket.on(`chatMessage`, message => {
      getGroupsWithUnreadCounts();
      console.log('chatMessage', message);
    });
    socket.on('disconnect', onDisconnect);

    return () => {
      socket.off('connect', onConnect);
      socket.off('disconnect', onDisconnect);
      socket.off('chatMessage');
    };
  }, []);

  const handleGroupClick = async (group: Group) => {
    // Check if user has permission to enter chat forum
    if (!canEnterChatForum) {
      Alert.alert(
        'Access Denied',
        "You don't have permission to access chat forums.",
        [{text: 'OK'}],
      );
      return;
    }

    try {
      const result = await GroupService.Groupcheck(userId, group.id);
      if (result.results) {
        // Check if user is blocked in this group
        try {
          const blockStatus = await GroupService.checkUserBlockStatus(
            userId,
            group.id,
          );
          console.log('🚫 Block status check result:', blockStatus);

          if (blockStatus.isBlocked) {
            Alert.alert(
              'Access Blocked',
              blockStatus.message ||
                `You are blocked from sending messages in "${group.name}" by an admin.`,
              [{text: 'OK'}],
            );
            return;
          }
        } catch (blockError) {
          console.log(
            '🚫 Block status check failed, proceeding anyway:',
            blockError,
          );
          // If block status check fails, proceed anyway (user might not be blocked)
        }

        await markMessagesAsRead(group.id, userId);
        // @ts-ignore
        navigation.navigate('chat', {
          groupId: group.id,
          groupName: group.name,
          groupimage: group.image,
        });
      } else {
        Alert.alert(
          'Join Group',
          'Are you sure you want to join this group?',
          [
            {
              text: 'No',
              style: 'cancel',
            },
            {
              text: 'Yes',
              onPress: async () => {
                try {
                  const joinResult = await GroupService.joinGroup(
                    userId,
                    group.id,
                  );
                  if (joinResult.success) {
                    setGroups(prevGroups =>
                      prevGroups.map(g =>
                        g.id === group.id ? {...g, joined: true} : g,
                      ),
                    );
                    await markMessagesAsRead(group.id, userId);
                    // @ts-ignore
                    navigation.navigate('chat', {
                      groupId: group.id,
                      groupName: group.name,
                      groupimage: group.image,
                    });
                  } else {
                    Alert.alert(
                      'Error',
                      'Failed to join the group. Please try again.',
                    );
                  }
                } catch (err) {
                  Alert.alert(
                    'Error',
                    'Failed to join the group. Please try again.',
                  );
                }
              },
            },
          ],
          {cancelable: true},
        );
      }
    } catch (err) {
      Alert.alert('Error', 'Failed to check group status. Please try again.');
    }
  };

  const handleImagePress = (image: string | undefined) => {
    setSelectedImage(image || null);
    setModalVisible(true);
  };

  const renderGroup = ({item, index}: {item: Group; index: number}) => (
    // <TouchableOpacity
    //     style={[styles.groupContainer, { backgroundColor: item.joined ? '#fff' : '#fff' }]}
    //     onPress={() => handleGroupClick(item)}
    // >
    <TouchableOpacity
      key={index}
      style={styles.groupContainer}
      onPress={() => handleGroupClick(item)}>
      <TouchableOpacity onPress={() => handleImagePress(item.image)}>
        <Image
          source={{
            uri:
              item.image ||
              'https://png.pngtree.com/template/20191005/ourmid/pngtree-logo-people-group-team-image_314502.jpg',
          }}
          style={styles.groupImage}
          resizeMode="stretch"
        />
      </TouchableOpacity>
      <View style={styles.groupInfo}>
        <Text style={styles.groupName}>{item.name}</Text>
        <Text style={styles.lastMessage}>
          {/* {item.username === "Unknown" ? item.lastMessage : `${item.username}: ${item.lastMessage}`} */}
          {item.username === 'Unknown'
            ? item.lastMessage
            : `${item.isCurrentUser ? 'You' : item.username}: ${
                item.lastMessage
              }`}
        </Text>
      </View>
      <View style={styles.rightSection}>
        <Text style={styles.timestamp}>
          {item.lastMessageTime}
          {/* {getCurrentTime()} */}
        </Text>
        {item.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>{item.unreadCount}</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator
          size="large"
          color="#00509e"
          style={{marginTop: normalize(20)}}
        />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.container}>
        <Text
          style={{
            textAlign: 'center',
            fontSize: 16,
            color: '#D32F2F',
          }}>
          {error}
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: 16,
          backgroundColor: '#002157',
          borderBottomWidth: 1,
          borderBottomColor: '#E0E0E0',
        }}>
        <Text style={styles.topBarTitle}>Group Forum</Text>
        <Ionicons name="people-circle-outline" size={40} color="#fff" />
      </View>
      <FlatList
        data={groups}
        renderItem={renderGroup}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.listContainer}
        // showsVerticalScrollIndicator={false}
      />
      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={{
              position: 'absolute',
              top: 40,
              right: 20,
            }}
            onPress={() => setModalVisible(false)}>
            <Ionicons name="close" size={28} color="#fff" />
          </TouchableOpacity>
          <Image
            source={{uri: selectedImage || ''}}
            style={styles.modalImage}
            resizeMode="contain"
          />
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: normalize(20),
    backgroundColor: '#F9FAFB',
  },
  topBar: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  topBarTitle: {
    fontSize: normalize(20),
    // color: '#000',
    color: 'white',
    fontWeight: '600',
    marginLeft: normalize(10),
  },

  listContainer: {
    // paddingHorizontal: normalize(1),
    padding: 16,
  },
  title: {
    fontSize: normalize(20),
    fontWeight: 'bold',
    marginBottom: normalize(15),
    color: '#00509e',
  },
  groupContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: normalize(12),
    marginBottom: normalize(10),
    backgroundColor: '#fff',
    borderRadius: normalize(5),
    shadowColor: '#000',
    shadowOpacity: 0.5,
    shadowOffset: {width: 10, height: 20},
    elevation: 2,
  },
  groupImage: {
    width: normalize(40),
    height: normalize(40),
    borderRadius: normalize(20),
    // borderWidth: 1,
    // borderColor: 'black',
    // overflow: 'hidden',
    // alignSelf: 'flex-start',
  },
  groupInfo: {
    flex: 1,
    marginLeft: 12,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000',
  },
  lastMessage: {
    fontSize: 14,
    color: 'gray',
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  timestamp: {
    fontSize: 12,
    color: 'gray',
  },
  unreadBadge: {
    backgroundColor: '#FF3B30',
    width: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },
  unreadText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  // groupName: {
  //     flex: 1,
  //     fontSize: normalize(16),
  //     color: '#00509e',
  //     textAlign: 'center',
  //     fontWeight: '800'
  // },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  modalImage: {
    width: '80%',
    height: '50%',
  },
});

export default GroupForumScreen;
