# Mobile App Permission Integration Guide

This guide shows how to integrate role-based permissions with the mobile app registration and throughout the application.

## 🚀 Quick Start

### 1. Permission System Overview

The mobile app now has a comprehensive permission system that:
- ✅ **Fetches permissions** from backend based on user role
- ✅ **Caches permissions** locally for offline access
- ✅ **Protects screens** with permission requirements
- ✅ **Hides UI elements** based on permissions
- ✅ **Initializes on login** and app startup

### 2. How It Works

#### **On User Registration/Login:**
1. User logs in with their role (GUEST, USER, HR, ADMIN)
2. Permission service fetches permissions from backend API
3. Permissions are cached locally for offline access
4. App components check permissions before rendering

#### **Permission Flow:**
```
Login → Fetch Role Permissions → Cache Locally → Check Permissions → Show/Hide Features
```

## 🔧 Implementation Examples

### 1. Protecting Entire Screens

Use `withPermission` HOC to protect entire screens:

```typescript
// AddPostScreen.tsx
import withPermission from '../../components/withPermission';

const AddPost = () => {
  // Your component code
};

// Protect the entire screen
export default withPermission(AddPost, {
  permission: 'ADD_POST',
  showUnauthorizedScreen: true,
});
```

**Result:** Users without ADD_POST permission see an "Access Denied" screen.

### 2. Protecting UI Elements

Use `PermissionGuard` to conditionally show UI elements:

```typescript
// HomeScreen.tsx
import PermissionGuard from '../../components/PermissionGuard';

const HomeScreen = () => {
  return (
    <View>
      {/* Only show Add Post button if user has permission */}
      <PermissionGuard permission="ADD_POST" showMessage={false}>
        <TouchableOpacity onPress={() => navigation.navigate('AddPost')}>
          <Text>Add Post</Text>
        </TouchableOpacity>
      </PermissionGuard>
    </View>
  );
};
```

**Result:** Add Post button only appears for users with ADD_POST permission.

### 3. Using Permission Hooks

Use hooks for custom permission logic:

```typescript
import { usePermissions } from '../hooks/usePermissions';

const MyComponent = () => {
  const { hasPermission, getUserRole } = usePermissions();

  const canCreateNotice = hasPermission('CREATE_NOTICE');
  const userRole = getUserRole();

  return (
    <View>
      {canCreateNotice && (
        <Button title="Create Notice" onPress={handleCreateNotice} />
      )}
      <Text>Your role: {userRole}</Text>
    </View>
  );
};
```

## 📱 Screen Protection Examples

### Already Protected Screens:

#### **AddPostScreen** - Requires `ADD_POST` permission
```typescript
export default withPermission(AddPost, {
  permission: 'ADD_POST',
  showUnauthorizedScreen: true,
});
```

#### **ChatForumScreen** - Requires `ENTER_CHAT_FORUM` permission
```typescript
export default withPermission(ChatForumScreen, {
  permission: 'ENTER_CHAT_FORUM',
  showUnauthorizedScreen: true,
});
```

### How to Protect More Screens:

```typescript
// NoticesScreen - View notices
export default withPermission(NoticesScreen, {
  permission: 'VIEW_NOTICES',
});

// ProfileScreen - View profiles
export default withPermission(ProfileScreen, {
  permission: 'VIEW_PROFILE',
});

// SettingsScreen - Multiple permissions (any one required)
export default withPermission(SettingsScreen, {
  permissions: ['EDIT_OWN_PROFILE', 'MANAGE_USERS'],
  requireAll: false, // User needs ANY of these permissions
});
```

## 🎯 Permission Categories & Usage

### **Post Management**
```typescript
// Create posts
<PermissionGuard permission="ADD_POST">
  <CreatePostButton />
</PermissionGuard>

// Edit own posts
<PermissionGuard permission="EDIT_OWN_POST">
  <EditPostButton />
</PermissionGuard>

// Delete any post (admin feature)
<PermissionGuard permission="DELETE_ANY_POST">
  <DeletePostButton />
</PermissionGuard>
```

### **Chat & Groups**
```typescript
// Enter chat forums
export default withPermission(ChatScreen, {
  permission: 'ENTER_CHAT_FORUM'
});

// Send messages
<PermissionGuard permission="SEND_MESSAGE_IN_FORUM">
  <MessageInput />
</PermissionGuard>

// Create groups
<PermissionGuard permission="CREATE_GROUP">
  <CreateGroupButton />
</PermissionGuard>
```

### **Profile Management**
```typescript
// View other profiles
<PermissionGuard permission="VIEW_OTHER_PROFILES">
  <UserProfileLink />
</PermissionGuard>

// Edit profile
<PermissionGuard permission="EDIT_OWN_PROFILE">
  <EditProfileButton />
</PermissionGuard>
```

### **Notices & Events**
```typescript
// View notices
<PermissionGuard permission="VIEW_NOTICES">
  <NoticesList />
</PermissionGuard>

// Create notices (HR/Admin only)
<PermissionGuard permission="CREATE_NOTICE">
  <CreateNoticeButton />
</PermissionGuard>
```

## 🔄 Permission Initialization

### Automatic Initialization:
1. **App Startup**: Loads cached permissions
2. **Login**: Fetches fresh permissions from server
3. **Registration**: Assigns default USER role permissions

### Manual Refresh:
```typescript
import permissionService from '../services/permissionService';

const refreshPermissions = async () => {
  try {
    await permissionService.refreshPermissions();
    console.log('Permissions refreshed');
  } catch (error) {
    console.error('Failed to refresh permissions');
  }
};
```

## 🛡️ Role-Based Access

### Default Role Permissions:

#### **GUEST** (5 permissions):
- VIEW_PROFILE, VIEW_NOTICES, VIEW_NOTICE_DETAILS, SHARE_APP, RECEIVE_NOTIFICATIONS

#### **USER** (24 permissions):
- All GUEST permissions + ADD_POST, EDIT_OWN_POST, LIKE_POST, COMMENT_ON_POST, ENTER_CHAT_FORUM, etc.

#### **HR** (33 permissions):
- All USER permissions + CREATE_NOTICE, MANAGE_GROUPS, MODERATE_POSTS, etc.

#### **ADMIN** (35 permissions):
- All HR permissions + DELETE_ANY_POST, MANAGE_USERS

## 🔍 Debugging & Testing

### Check User Permissions:
```typescript
import { usePermissions } from '../hooks/usePermissions';

const DebugComponent = () => {
  const { getUserPermissions, getUserRole, isPermissionsLoaded } = usePermissions();

  if (!isPermissionsLoaded) {
    return <Text>Loading permissions...</Text>;
  }

  const permissions = getUserPermissions();
  const role = getUserRole();

  return (
    <View>
      <Text>Role: {role}</Text>
      <Text>Permissions: {permissions.length}</Text>
      {permissions.map(perm => (
        <Text key={perm}>• {perm}</Text>
      ))}
    </View>
  );
};
```

### Console Logs:
The permission system logs important events:
- Permission initialization
- API calls
- Permission checks
- Cache operations

## 🎉 Benefits

✅ **Secure**: Only authorized users can access features
✅ **Flexible**: Easy to add/remove permissions per role
✅ **Offline**: Cached permissions work without internet
✅ **User-Friendly**: Clear messages when access is denied
✅ **Maintainable**: Centralized permission management

The mobile app now has comprehensive role-based permission control integrated throughout the application!
