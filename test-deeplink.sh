#!/bin/bash

# Alumni App Deep Link Test Script
echo "🔗 Alumni App Deep Link Tester"
echo "================================"

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo "❌ ADB not found. Please install Android SDK Platform Tools."
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ No Android device/emulator connected."
    echo "Please connect your device or start an emulator."
    exit 1
fi

echo "✅ ADB found and device connected"

# Package name
PACKAGE_NAME="com.sgicalumni"

# Test token
TOKEN="0c6ec490fda713e6813dfab0d117278336bbaa42ebed7dea9b0512b2ff2cb829"

echo ""
echo "📱 Testing Deep Links..."
echo "Package: $PACKAGE_NAME"
echo "Token: $TOKEN"
echo ""

# Test 1: Check if app is installed
echo "1️⃣ Checking if app is installed..."
if adb shell pm list packages | grep -q "$PACKAGE_NAME"; then
    echo "✅ App is installed"
else
    echo "❌ App is not installed. Please build and install the app first."
    echo "Run: npx react-native run-android"
    exit 1
fi

# Test 2: Test basic deep link
echo ""
echo "2️⃣ Testing basic deep link..."
adb shell am start -W -a android.intent.action.VIEW -d "alumniapp://login" "$PACKAGE_NAME" 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Basic deep link test completed"
else
    echo "❌ Basic deep link test failed"
fi

sleep 2

# Test 3: Test reset password deep link
echo ""
echo "3️⃣ Testing reset password deep link..."
adb shell am start -W -a android.intent.action.VIEW -d "alumniapp://reset-password?token=$TOKEN" "$PACKAGE_NAME" 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Reset password deep link test completed"
else
    echo "❌ Reset password deep link test failed"
fi

sleep 2

# Test 4: Check app logs
echo ""
echo "4️⃣ Checking app logs for deep link activity..."
echo "Looking for MainActivity logs..."
adb logcat -d | grep "MainActivity" | tail -10

echo ""
echo "Looking for React Native logs..."
adb logcat -d | grep "ReactNativeJS" | tail -5

echo ""
echo "🔍 Troubleshooting Tips:"
echo "========================"
echo "1. Make sure you rebuilt the app after adding deep link config:"
echo "   npx react-native clean && npx react-native run-android"
echo ""
echo "2. Check if the app opens when running the commands above"
echo ""
echo "3. Look at the logs above for any error messages"
echo ""
echo "4. If deep links don't work, use the test button in the app:"
echo "   Open app → Login screen → 'Test Reset Password' button"
echo ""
echo "5. Check Android manifest has the correct intent filters"
echo ""
echo "6. Verify the package name matches: $PACKAGE_NAME"

echo ""
echo "📋 Manual Test Commands:"
echo "========================"
echo "Basic deep link:"
echo "adb shell am start -W -a android.intent.action.VIEW -d \"alumniapp://login\" \"$PACKAGE_NAME\""
echo ""
echo "Reset password deep link:"
echo "adb shell am start -W -a android.intent.action.VIEW -d \"alumniapp://reset-password?token=$TOKEN\" \"$PACKAGE_NAME\""
echo ""
echo "Check logs:"
echo "adb logcat | grep -E \"(MainActivity|ReactNativeJS)\""
