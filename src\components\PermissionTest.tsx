import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from './PermissionGuard';
import permissionService from '../services/permissionService';

const PermissionTest: React.FC = () => {
  const { 
    canAddPost, 
    canLikePost, 
    canCommentOnPost, 
    canEnterChatForum,
    getUserRole,
    isPermissionsLoaded 
  } = usePermissions();

  const role = getUserRole();
  const permissions = permissionService.getUserPermissions();

  const testPermissionCheck = (permission: string) => {
    console.log(`Testing permission: ${permission}`);
    switch (permission) {
      case 'ADD_POST':
        console.log(`Can add post: ${canAddPost}`);
        break;
      case 'LIKE_POST':
        console.log(`Can like post: ${canLikePost}`);
        break;
      case 'COMMENT_ON_POST':
        console.log(`Can comment: ${canCommentOnPost}`);
        break;
      case 'ENTER_CHAT_FORUM':
        console.log(`Can enter chat: ${canEnterChatForum}`);
        break;
    }
  };

  if (!isPermissionsLoaded) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Loading Permissions...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Permission Test Screen</Text>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>User Info</Text>
        <Text style={styles.item}>Role: {role || 'Unknown'}</Text>
        <Text style={styles.item}>Permissions Loaded: {isPermissionsLoaded ? 'Yes' : 'No'}</Text>
        <Text style={styles.item}>Permission Count: {permissions.length}</Text>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Permissions</Text>
        {permissions.length > 0 ? (
          permissions.map((permission, index) => (
            <Text key={index} style={styles.permission}>
              ✅ {permission}
            </Text>
          ))
        ) : (
          <Text style={styles.item}>No permissions found</Text>
        )}
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Permission Tests</Text>
        
        <TouchableOpacity 
          style={styles.testButton}
          onPress={() => testPermissionCheck('ADD_POST')}
        >
          <Text style={styles.buttonText}>Test ADD_POST</Text>
          <Text style={styles.result}>{canAddPost ? '✅ Allowed' : '❌ Denied'}</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton}
          onPress={() => testPermissionCheck('LIKE_POST')}
        >
          <Text style={styles.buttonText}>Test LIKE_POST</Text>
          <Text style={styles.result}>{canLikePost ? '✅ Allowed' : '❌ Denied'}</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton}
          onPress={() => testPermissionCheck('COMMENT_ON_POST')}
        >
          <Text style={styles.buttonText}>Test COMMENT_ON_POST</Text>
          <Text style={styles.result}>{canCommentOnPost ? '✅ Allowed' : '❌ Denied'}</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.testButton}
          onPress={() => testPermissionCheck('ENTER_CHAT_FORUM')}
        >
          <Text style={styles.buttonText}>Test ENTER_CHAT_FORUM</Text>
          <Text style={styles.result}>{canEnterChatForum ? '✅ Allowed' : '❌ Denied'}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>PermissionGuard Tests</Text>
        
        <PermissionGuard permission="ADD_POST" showMessage={true}>
          <View style={styles.guardedItem}>
            <Text style={styles.guardedText}>🎯 ADD_POST Protected Content</Text>
          </View>
        </PermissionGuard>

        <PermissionGuard permission="LIKE_POST" showMessage={true}>
          <View style={styles.guardedItem}>
            <Text style={styles.guardedText}>❤️ LIKE_POST Protected Content</Text>
          </View>
        </PermissionGuard>

        <PermissionGuard permission="COMMENT_ON_POST" showMessage={true}>
          <View style={styles.guardedItem}>
            <Text style={styles.guardedText}>💬 COMMENT_ON_POST Protected Content</Text>
          </View>
        </PermissionGuard>

        <PermissionGuard permission="ENTER_CHAT_FORUM" showMessage={true}>
          <View style={styles.guardedItem}>
            <Text style={styles.guardedText}>🗨️ ENTER_CHAT_FORUM Protected Content</Text>
          </View>
        </PermissionGuard>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#2196F3',
  },
  item: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  permission: {
    fontSize: 14,
    marginBottom: 4,
    color: '#4CAF50',
    paddingLeft: 8,
  },
  testButton: {
    backgroundColor: '#2196F3',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  result: {
    color: '#fff',
    fontSize: 12,
  },
  guardedItem: {
    backgroundColor: '#E8F5E8',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    borderLeft: 4,
    borderLeftColor: '#4CAF50',
  },
  guardedText: {
    color: '#2E7D32',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default PermissionTest;
