import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import {NavigationProp, RouteProp} from '@react-navigation/native';
import authService from '../services/authService';
import Colors from '../constants/Colors';
import {differenceInYears, differenceInMonths} from 'date-fns';

type ViewUserProfileProps = {
  navigation: NavigationProp<any>;
  route: RouteProp<any>;
};

const ViewUserProfile: React.FC<ViewUserProfileProps> = ({
  navigation,
  route,
}) => {
  const {userId, userName} = route.params as {userId: number; userName: string};
  const [userInfo, setUserInfo] = useState<any>(null);
  const [userSkills, setUserSkills] = useState<any[]>([]);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [loadingSkills, setLoadingSkills] = useState(false);

  const fetchUserDetails = useCallback(async () => {
    setLoadingDetails(true);
    try {
      const res = await authService.getUserDetails(userId);
      setUserInfo(res?.data);
    } catch (error) {
      console.error('Error fetching user details:', error);
    } finally {
      setLoadingDetails(false);
    }
  }, [userId]);

  const fetchUserSkills = useCallback(async () => {
    setLoadingSkills(true);
    try {
      const res = await authService.getAllSkills(userId);
      setUserSkills(res?.data || []);
    } catch (error) {
      console.error('Error fetching user skills:', error);
      setUserSkills([]);
    } finally {
      setLoadingSkills(false);
    }
  }, [userId]);

  useEffect(() => {
    fetchUserDetails();
    fetchUserSkills();
  }, [fetchUserDetails, fetchUserSkills]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
    });
  };

  const calculateDuration = (startDate: any, endDate: any) => {
    if (!startDate) return '';
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const years = differenceInYears(end, start);
    const months = differenceInMonths(end, start) % 12;

    let duration = '';
    if (years > 0) duration += `${years} yr${years > 1 ? 's' : ''}`;
    if (months > 0)
      duration += `${years > 0 ? ' ' : ''}${months} mo${months > 1 ? 's' : ''}`;
    return duration;
  };

  // Sort experiences by start date (most recent first)
  const sortedExperiences =
    userInfo?.employmentHistory?.sort((a: any, b: any) => {
      const dateA = new Date(a.startDate || 0);
      const dateB = new Date(b.startDate || 0);
      return dateB.getTime() - dateA.getTime();
    }) || [];

  if (loadingDetails) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#002157" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{'User Profile'}</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.scrollContainer}>
        {/* Profile Header */}

        {/* User Info */}
        <View style={styles.userInfoContainer}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{
                uri:
                  userInfo?.image ||
                  'https://banner2.cleanpng.com/20180611/voc/aa8otwe2u.webp',
              }}
              style={styles.profileImage}
            />
          </View>

          <Text style={styles.userName}>{userName}</Text>
          {userInfo?.currentPosition && (
            <Text style={styles.userPosition}>{userInfo.currentPosition}</Text>
          )}
          {userInfo?.city && (
            <View style={styles.locationContainer}>
              <Ionicons name="location-outline" size={16} color="#666" />
              <Text style={styles.locationText}>{userInfo.city}</Text>
            </View>
          )}
        </View>

        {/* Personal Information */}
        {(userInfo?.email || userInfo?.phone || userInfo?.dateOfBirth) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Personal Information</Text>
            <View style={styles.infoContainer}>
              {userInfo?.email && (
                <View style={styles.infoItem}>
                  <FontAwesome5 name="envelope" size={16} color="#0072b1" />
                  <Text style={styles.infoText}>{userInfo.email}</Text>
                </View>
              )}
              {userInfo?.phone && (
                <View style={styles.infoItem}>
                  <FontAwesome5 name="phone" size={16} color="#0072b1" />
                  <Text style={styles.infoText}>{userInfo.phone}</Text>
                </View>
              )}
              {userInfo?.dateOfBirth && (
                <View style={styles.infoItem}>
                  <FontAwesome5
                    name="birthday-cake"
                    size={16}
                    color="#0072b1"
                  />
                  <Text style={styles.infoText}>
                    {formatDate(userInfo.dateOfBirth)}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Skills Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Skills & Expertise</Text>
          {loadingSkills ? (
            <View style={styles.skillsLoadingContainer}>
              <ActivityIndicator size="small" color={Colors.primary} />
              <Text style={styles.loadingText}>Loading skills...</Text>
            </View>
          ) : userSkills && userSkills.length > 0 ? (
            <View style={styles.skillsTagContainer}>
              {userSkills.map((skill: any, index: number) => (
                <View key={index} style={styles.skillTag}>
                  <Text style={styles.skillTagText}>{skill.skillName}</Text>
                  {skill.level && (
                    <View style={styles.skillTagLevel}>
                      <Text style={styles.skillTagLevelText}>
                        {skill.level}/5
                      </Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <FontAwesome5 name="code" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No skills shared</Text>
            </View>
          )}
        </View>

        {/* Experience Timeline */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Experience Timeline</Text>
          {sortedExperiences && sortedExperiences.length > 0 ? (
            <View style={styles.timelineContainer}>
              {sortedExperiences.map((exp: any, index: number) => (
                <View key={index} style={styles.timelineItem}>
                  {/* Timeline Line */}
                  <View style={styles.timelineLineContainer}>
                    <View style={styles.timelineDot} />
                    {index < sortedExperiences.length - 1 && (
                      <View style={styles.timelineLine} />
                    )}
                  </View>

                  {/* Experience Card */}
                  <View style={styles.experienceCard}>
                    <View style={styles.experienceMainInfo}>
                      <Text style={styles.companyName}>{exp.companyName}</Text>
                      <Text style={styles.positionTitle}>
                        {exp.position} ({exp.positionPrefix})
                      </Text>
                      <Text style={styles.stackInfo}>{exp.stack}</Text>
                    </View>

                    <View style={styles.experienceDetails}>
                      <View style={styles.dateContainer}>
                        <FontAwesome5
                          name="calendar-alt"
                          size={12}
                          color="#666"
                        />
                        <Text style={styles.dateText}>
                          {formatDate(exp.startDate)} -{' '}
                          {exp.endDate ? formatDate(exp.endDate) : 'Present'}
                        </Text>
                      </View>
                      <View style={styles.durationContainer}>
                        <FontAwesome5 name="clock" size={12} color="#666" />
                        <Text style={styles.durationText}>
                          {calculateDuration(exp.startDate, exp.endDate)}
                        </Text>
                      </View>
                      {exp.salaries && (
                        <View style={styles.salaryContainer}>
                          <FontAwesome5
                            name="dollar-sign"
                            size={12}
                            color="#666"
                          />
                          <Text style={styles.salaryText}>{exp.salaries}</Text>
                        </View>
                      )}
                    </View>

                    {/* Status Badge */}
                    <View style={styles.statusBadge}>
                      <Text
                        style={[
                          styles.statusText,
                          exp.endDate ? styles.pastJob : styles.currentJob,
                        ]}>
                        {exp.endDate ? 'Past' : 'Current'}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <FontAwesome5 name="briefcase" size={48} color="#ccc" />
              <Text style={styles.emptyText}>No work experience shared</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#002157',
    paddingHorizontal: 16,
    paddingVertical: 8,
    paddingTop: StatusBar.currentHeight ? StatusBar.currentHeight + 8 : 8,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    width: 40,
  },
  scrollContainer: {
    flex: 1,
  },
  coverImage: {
    height: 140,
    justifyContent: 'flex-end',
    alignItems: 'center',
    backgroundColor: '#0072b1',
  },
  profileImageContainer: {
    marginBottom: 0,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 40,
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    // elevation: 4,
  },
  userInfoContainer: {
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 16,
    backgroundColor: '#fff',
  },
  userName: {
    fontSize: 14,
    color: '#0072b1',
    fontWeight: '600',
    marginBottom: 6,
  },
  userPosition: {
    fontSize: 14,
    color: '#0072b1',
    fontWeight: '600',
    marginBottom: 6,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 6,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginHorizontal: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  infoContainer: {
    gap: 12,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  timelineContainer: {
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineLineContainer: {
    alignItems: 'center',
    marginRight: 16,
    width: 20,
  },
  timelineDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#0072b1',
    borderWidth: 2,
    borderColor: '#fff',
    shadowColor: '#0072b1',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#e1e8ed',
    marginTop: 8,
  },
  experienceCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#0072b1',
  },
  experienceMainInfo: {
    marginBottom: 8,
  },
  companyName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 3,
  },
  positionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0072b1',
    marginBottom: 3,
  },
  stackInfo: {
    fontSize: 13,
    color: '#666',
    fontStyle: 'italic',
  },
  experienceDetails: {
    marginBottom: 8,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  dateText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  durationText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  salaryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  salaryText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 6,
    fontWeight: '500',
  },
  statusBadge: {
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  currentJob: {
    backgroundColor: '#e8f5e8',
    color: '#2d7d32',
  },
  pastJob: {
    backgroundColor: '#f3f4f6',
    color: '#6b7280',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 16,
  },
  // Skills Section Styles
  skillsLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  skillsTagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  skillTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  skillTagText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#fff',
  },
  skillTagLevel: {
    marginLeft: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
  },
  skillTagLevelText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
});

export default ViewUserProfile;
