@echo off
echo 🔧 Building Alumni Mobile App for Android
echo ==========================================

echo.
echo 📱 Step 1: Cleaning previous builds...
call npx react-native clean

echo.
echo 🧹 Step 2: Cleaning Android build...
cd android
call gradlew clean


echo.
echo 🔨 Step 4: Building Android APK...
echo This may take a few minutes...

REM Set environment variables for better build
set ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk
set JAVA_HOME=C:\Program Files\Java\jdk-17

echo.
echo 🔧 Step 4a: Syncing Gradle dependencies...
cd android
call gradlew --refresh-dependencies
cd ..

echo.
echo 📱 Step 4b: Building and installing app...
echo Trying build without new architecture first...
call npx react-native run-android --variant=debug

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ⚠️  Build failed, trying alternative approach...
    echo Building APK directly with Gradle...
    cd android
    call gradlew assembleDebug
    cd ..

    echo.
    echo 📱 Installing APK manually...
    adb install android\app\build\outputs\apk\debug\app-debug.apk
)

echo.
echo ✅ Build completed!
echo Check the output above for any errors.

pause
