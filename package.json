{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gluestack-ui/actionsheet": "^0.2.46", "@gorhom/bottom-sheet": "^5.0.6", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.0", "@react-native-community/datetimepicker": "^8.2.0", "@react-native-firebase/app": "^21.6.2", "@react-native-firebase/messaging": "^21.6.2", "@react-native-picker/picker": "^2.11.0", "@react-native/gradle-plugin": "^0.76.5", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@reduxjs/toolkit": "^2.5.0", "axios": "^1.7.9", "date-fns": "^4.1.0", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "18.3.1", "react-native": "0.76.5", "react-native-calendars": "^1.1307.0", "react-native-date-picker": "^5.0.8", "react-native-dotenv": "^3.4.11", "react-native-dropdown-picker": "^5.4.6", "react-native-gesture-handler": "^2.18.0", "react-native-image-picker": "^7.2.3", "react-native-image-viewing": "^0.2.2", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.13.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-audio-recorder-player": "^3.6.10", "@react-native-community/slider": "^4.5.2", "react-native-permissions": "^4.1.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.0.0", "react-native-screens": "^4.4.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/lodash": "^4.17.14", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}