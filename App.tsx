import React, {useEffect} from 'react';
import {Alert, Platform, StatusBar} from 'react-native';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import notifee, {
  AndroidImportance,
  AndroidStyle,
  EventType,
  AuthorizationStatus,
} from '@notifee/react-native';
import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import {Provider} from 'react-redux';
import {PersistGate} from 'redux-persist/integration/react';
import {store, persistor} from './src/store/store';
import Navigation from './src/navigation';
import Colors from './src/constants/Colors';
import {AlertNotificationRoot} from './src/components/CustomAlert';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  handlePendingNavigation,
  navigate,
  navigationRef,
} from './src/navigationpoint';
import {NotificationProvider} from './src/contexts/NotificationContext';
import permissionService from './src/services/permissionService';
import AppWithBlockedAlert from './src/components/AppWithBlockedAlert';

export default function App() {
  useEffect(() => {
    checkAndRequestPermission();
    startForegroundService();

    // Initialize permissions from cache on app startup
    initializePermissions();
  }, []);

  const initializePermissions = async () => {
    try {
      const initialized = await permissionService.loadFromCache();
      if (initialized) {
        console.log('✅ Permissions loaded from cache on app startup');
      } else {
        console.log('ℹ️ No cached permissions found, will initialize on login');
      }
    } catch (error) {
      console.error(
        '❌ Failed to initialize permissions on app startup:',
        error,
      );
    }
  };

  useEffect(() => {
    // start();
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));

      // Display notification when app is in foreground
      try {
        if (remoteMessage.data?.body) {
          const messageData = JSON.parse(String(remoteMessage.data.body));

          // Create notification channel
          const channelId = await notifee.createChannel({
            id: 'default',
            name: 'Default Channel',
            importance: AndroidImportance.HIGH,
          });

          // Display the notification
          await notifee.displayNotification({
            title: String(remoteMessage.data.title) || 'New Message',
            body: messageData.content || 'You have a new message',
            android: {
              channelId,
              importance: AndroidImportance.HIGH,
              smallIcon: 'ic_launcher',
              pressAction: {
                id: 'default',
              },
              color: '#4caf50',
            },
            data: {
              body: remoteMessage.data.body,
              type: messageData.type,
              // Only include groupId, groupName, groupLogo for CHAT notifications
              ...(messageData.type === 'CHAT' && {
                groupId: messageData.groupId,
                groupName: messageData.groupName,
                groupLogo: messageData.groupLogo,
              }),
              // For NOTICE notifications, include image only if it's a valid string
              ...(messageData.type === 'NOTICE' &&
                messageData.image &&
                typeof messageData.image === 'string' && {
                  image: messageData.image,
                }),
              // For POST_LIKE notifications
              ...(messageData.type === 'POST_LIKE' && {
                postId: messageData.postId?.toString() || '',
                likerName: messageData.likerName || '',
              }),
              // For POST_COMMENT notifications
              ...(messageData.type === 'POST_COMMENT' && {
                postId: messageData.postId?.toString() || '',
                commenterName: messageData.commenterName || '',
                commentText: messageData.commentText || '',
              }),
            },
          });
        }
      } catch (error) {
        console.error('Error displaying foreground notification:', error);
        console.error('Message data that caused error:', remoteMessage.data);
      }
    });
    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  async function checkAndRequestPermission() {
    const authStatus = await messaging().hasPermission();
    if (
      authStatus === messaging.AuthorizationStatus.NOT_DETERMINED ||
      authStatus === messaging.AuthorizationStatus.DENIED
    ) {
      Alert.alert(
        'Notification Permission Required',
        'We need your permission to send notifications. Please enable it in settings.',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Enable',
            onPress: async () => {
              if (Platform.OS === 'android') {
                openNotificationSettings();
              } else {
                const requestStatus = await messaging().requestPermission();
                handlePermissionStatus(requestStatus);
              }
            },
          },
        ],
      );
    } else {
      console.log('Notification permission already granted:', authStatus);
    }

    // Also request notifee permission for displaying notifications
    try {
      const settings = await notifee.requestPermission();
      if (settings.authorizationStatus === AuthorizationStatus.DENIED) {
        console.log('User denied notifee permissions request');
      } else if (
        settings.authorizationStatus === AuthorizationStatus.AUTHORIZED
      ) {
        console.log('User granted notifee permissions request');
      } else if (
        settings.authorizationStatus === AuthorizationStatus.PROVISIONAL
      ) {
        console.log('User provisionally granted notifee permissions request');
      }
    } catch (error) {
      console.error('Error requesting notifee permission:', error);
    }
  }

  async function handlePermissionStatus(
    status: FirebaseMessagingTypes.AuthorizationStatus,
  ) {
    if (
      status === messaging.AuthorizationStatus.AUTHORIZED ||
      status === messaging.AuthorizationStatus.PROVISIONAL
    ) {
      console.log('Notification permission granted:', status);
    } else {
      console.log('Notification permission denied:', status);
    }
  }

  async function openNotificationSettings() {
    try {
      await notifee.openNotificationSettings();
    } catch (error) {
      console.error('Failed to open notification settings:', error);
    }
  }

  async function startForegroundService() {
    // Create a channel for the foreground service notification
    const channelId = await notifee.createChannel({
      id: 'foreground-service',
      name: 'Foreground Service',
      importance: AndroidImportance.HIGH, // Ensures the notification is shown immediately
    });

    // Display the notification

    // await notifee.displayNotification({
    //   title: 'Foreground Service Running',
    //   body: 'Your app is performing a task in the background.',
    //   android: {
    //     channelId,
    //     asForegroundService: true, // Mark this notification as a foreground service
    //     smallIcon: 'ic_launcher', // Ensure this icon exists in res/drawable
    //     // importance: AndroidImportance.HIGH
    //   },
    // });
  }

  async function stopForegroundService() {
    await notifee.stopForegroundService();
  }

  // useEffect(() => {
  //   startForegroundService();
  //   return () => {
  //     stopForegroundService(); // Stop service when the component unmounts
  //   };
  // }, []);

  // Handle notification when app is opened from background
  useEffect(() => {
    const unsubscribe = messaging().onNotificationOpenedApp(remoteMessage => {
      console.log(
        'Notification caused app to open from background state:',
        remoteMessage,
      );

      if (remoteMessage.data?.body) {
        try {
          const messageData = JSON.parse(String(remoteMessage.data.body));
          const {groupId, groupName, groupLogo} = messageData;

          if (messageData.type === 'CHAT') {
            navigate('chat', {
              groupId: groupId,
              groupName: groupName,
              groupimage: groupLogo,
            });
          } else if (messageData.type === 'NOTICE') {
            navigate('Notice');
          } else if (
            messageData.type === 'POST_LIKE' ||
            messageData.type === 'POST_COMMENT' ||
            messageData.type === 'NEW_POST'
          ) {
            navigate('Home');
          }
        } catch (error) {
          console.error('Error parsing notification data:', error);
        }
      }
    });

    // Check whether an initial notification is available
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log(
            'Notification caused app to open from quit state:',
            remoteMessage,
          );

          if (remoteMessage.data?.body) {
            try {
              const messageData = JSON.parse(String(remoteMessage.data.body));
              const {groupId, groupName, groupLogo} = messageData;

              if (messageData.type === 'CHAT') {
                navigate('chat', {
                  groupId: groupId,
                  groupName: groupName,
                  groupimage: groupLogo,
                });
              } else if (
                messageData.type === 'NOTICE' ||
                messageData.type === 'EVENT'
              ) {
                if (messageData.noticeId) {
                  navigate('NoticeDetail', {
                    noticeId: messageData.noticeId,
                  });
                } else {
                  navigate('Notice');
                }
              } else if (
                messageData.type === 'POST_LIKE' ||
                messageData.type === 'POST_COMMENT' ||
                messageData.type === 'NEW_POST'
              ) {
                navigate('Home');
              }
            } catch (error) {
              console.error('Error parsing notification data:', error);
            }
          }
        }
      });

    return unsubscribe;
  }, []);

  useEffect(() => {
    if (navigationRef.isReady()) {
      handlePendingNavigation();
    }
  }, []);

  useEffect(() => {
    const unsubscribe = notifee.onForegroundEvent(
      async ({type, detail}: any) => {
        const {notification, pressAction} = detail;
        console.log('notification:-----for:---------------', notification);

        switch (type) {
          case EventType.PRESS:
            if (pressAction?.id === 'default') {
              try {
                // Use the data we stored in the notification
                const notificationData = notification.data;
                const messageType = notificationData?.type;

                if (messageType === 'CHAT') {
                  const groupId = notificationData?.groupId;
                  const groupName = notificationData?.groupName;
                  const groupLogo = notificationData?.groupLogo;

                  navigate('chat', {
                    groupId: groupId,
                    groupName: groupName,
                    groupimage: groupLogo,
                  });
                } else if (
                  messageType === 'NOTICE' ||
                  messageType === 'EVENT'
                ) {
                  const noticeId = notificationData?.noticeId;
                  if (noticeId) {
                    navigate('NoticeDetail', {
                      noticeId: noticeId,
                    });
                  } else {
                    navigate('Notice');
                  }
                } else if (
                  messageType === 'POST_LIKE' ||
                  messageType === 'POST_COMMENT'
                ) {
                  navigate('Home');
                }
              } catch (error) {
                console.error('Error handling notification press:', error);
              }
            }
            break;
        }
      },
    );

    return unsubscribe;
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <SafeAreaProvider>
          <AlertNotificationRoot>
            <NotificationProvider>
              <AppWithBlockedAlert />
            </NotificationProvider>
          </AlertNotificationRoot>
        </SafeAreaProvider>
      </PersistGate>
    </Provider>
  );
}
