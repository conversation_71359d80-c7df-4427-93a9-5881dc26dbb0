import {
  Safe<PERSON>reaView,
  <PERSON>rollView,
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Image,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import normalize from '../../types/utiles';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useSelector} from 'react-redux';
import {
  ImageLibraryOptions,
  ImagePickerResponse,
  launchImageLibrary,
} from 'react-native-image-picker';
import {UpdateUserPost} from '../../services/postServices';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {ALERT_TYPE, Dialog, Toast} from '../../components/CustomAlert';

const UpdatePostScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const {postId, postImages, postText, userId, postImagesIds} =
    route.params as {
      postId: number;
      userId: number;
      postImages: string[];
      postImagesIds: string;
      postText: string;
    };
  const [content, setContent] = useState(postText || '');
  const [images, setImages] = useState(
    postImages.map(uri => ({
      uri: uri,
      type: 'image/jpeg',
      fileName: uri.split('/').pop() || 'unknown',
    })),
  );
  const [selectedImages, setSelectedImages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const userDetsils = useSelector((state: any) => state.auth.userDetails);

  const handleImagePicker = async () => {
    try {
      const options: ImageLibraryOptions = {
        mediaType: 'photo',
        selectionLimit: 0,
      };
      const response: ImagePickerResponse = await new Promise(resolve => {
        launchImageLibrary(options, resolve);
      });

      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorMessage) {
        console.error('Image Picker Error: ', response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const pickedImages = response.assets.map(image => ({
          uri: image.uri || '',
          type: image.type || '',
          fileName: image.fileName || '',
        }));
        setImages(prevImages => [...prevImages, ...pickedImages]);
        setSelectedImages(prevImages => [...prevImages, ...pickedImages]);
      } else {
        console.warn('No images selected');
      }
    } catch (error) {
      console.error('Error during image selection:', error);
    }
  };

  const handleDeleteImage = (index: any) => {
    const updatedImages = images.filter((_, i) => i !== index);
    setImages(updatedImages);
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const result = await UpdateUserPost(
        userId,
        postId,
        postImagesIds,
        selectedImages,
        content,
      );

      Dialog.show({
        type: ALERT_TYPE.INFO,
        title: 'Success',
        textBody: 'Post updated successfully!',
        button: 'OK',
        onPressButton: () => {
          Dialog.hide();
          setTimeout(() => {
            navigation.navigate('PostLogin');
          }, 100);
        },
      });
      // Toast.show({
      //     type: ALERT_TYPE.INFO,
      //     title: 'Post updated successfully!',

      //     autoClose: 100000, // Auto-close the toast after 3 seconds
      //     titleStyle: {
      //         color: 'black',
      //         fontSize: normalize(12),         // Larger font size for the title
      //         marginTop: normalize(10),     // Margin below the title
      //     },
      //     style: {
      //         backgroundColor: '#000',
      //         marginTop: normalize(10),
      //         padding: normalize(10),
      //         borderRadius: normalize(10),
      //     },
      //     onShow: () => {
      //         console.log('Toast shown');
      //     },
      //     onHide: () => {
      //         console.log('Toast hidden');
      //         // Automatically navigate to 'PostLogin' when the toast is hidden
      //         navigation.navigate('PostLogin');
      //     },

      // });
    } catch (error) {
      setIsLoading(false);
      Dialog.show({
        type: ALERT_TYPE.WARNING,
        title: 'Failed to update post. Please try again.',
        // textBody: 'Change Your Password',
        button: 'OK',
        onPressButton: () => {
          Dialog.hide();
        },
      });
      console.error('Error updating post:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.navigate('PostLogin')}>
            <Text style={styles.closeIcon}>✕</Text>
          </TouchableOpacity>

          <View style={styles.rightHeader}>
            <TouchableOpacity style={styles.avatarContainer}>
              <Image source={{uri: userDetsils?.image}} style={styles.avatar} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.postButton,
                {backgroundColor: images.length > 0 ? '#007AFF' : '#e5e5e5'},
              ]}
              onPress={handleSubmit}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.postButtonText}>Update</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.content}>
          <TextInput
            style={styles.input}
            placeholder="Update your post..."
            placeholderTextColor="#666"
            multiline
            textAlignVertical="top"
            onChangeText={setContent}
            value={content}
          />

          <View style={styles.uploadContainer}>
            {images.length > 0 && (
              <FlatList
                data={images}
                keyExtractor={(item, index) => index.toString()}
                numColumns={3}
                renderItem={({item, index}) => (
                  <View style={styles.imagePreview}>
                    <Image
                      source={{uri: item.uri}}
                      style={styles.previewImage}
                    />
                    <TouchableOpacity
                      style={styles.deleteIcon}
                      onPress={() => handleDeleteImage(index)}>
                      <Text style={styles.deleteText}>✕</Text>
                    </TouchableOpacity>
                  </View>
                )}
              />
            )}
          </View>
        </View>
      </ScrollView>
      <TouchableOpacity style={styles.uploadButton} onPress={handleImagePicker}>
        <Icon name="add-photo-alternate" size={30} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

export default UpdatePostScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    position: 'relative',
  },
  scrollContainer: {
    paddingBottom: normalize(300),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: normalize(16),
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeIcon: {
    fontSize: normalize(20),
    color: '#333',
  },
  rightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  avatar: {
    width: 32,
    height: 32,
    backgroundColor: '#e5e5e5',
    borderRadius: 16,
  },
  postButton: {
    backgroundColor: '#e5e5e5',
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  postButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  content: {
    padding: 16,
  },
  input: {
    fontSize: normalize(15),
    color: '#333',
    minHeight: normalize(180),
    padding: 12,
    borderRadius: 8,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 16,
    fontFamily: 'Arial',
  },
  uploadContainer: {
    alignItems: 'center',
  },
  imagePreview: {
    position: 'relative',
    margin: normalize(5),
    aspectRatio: 1,
    width: '30%',
  },
  previewImage: {
    width: normalize(90),
    height: normalize(90),
    borderRadius: 10,
  },
  deleteIcon: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: normalize(10),
    padding: 2,
  },
  deleteText: {
    color: 'white',
    fontSize: normalize(12),
  },
  uploadButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: '#007AFF',
    padding: 16,
    borderRadius: 30,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
    zIndex: 10,
  },
});
