import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

import AsyncStorage from '@react-native-async-storage/async-storage';

export const baseURL = 'http://74.235.80.66:3009/api/v1/';
// export const baseURL = 'http://34.57.197.188:3001/api/v1/';

const apiClient = axios.create({
  baseURL,
});

apiClient.interceptors.request.use(
  async (
    config: AxiosRequestConfig,
  ): Promise<InternalAxiosRequestConfig<any>> => {
    const accessToken = await AsyncStorage.getItem('accessToken');
    if (accessToken) {
      console.log('accessToken', accessToken);

      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config as InternalAxiosRequestConfig<any>;
  },
  error => {
    return Promise.reject(error);
  },
);

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async error => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        const refreshToken = await AsyncStorage.getItem('refreshToken');
        const response = await apiClient.post('auth/refresh-token', {
          refreshToken: refreshToken,
        });

        const newAccessToken = response.data.accessToken;
        await AsyncStorage.setItem('accessToken', newAccessToken);

        // Update the original request with the new access token
        originalRequest.headers = originalRequest.headers || {};
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

        // Retry the original request
        return apiClient(originalRequest);
      } catch (refreshError) {
        console.error('Refresh token failed:', refreshError);
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  },
);

export default apiClient;
