import apiClient from './axios';
import apiService from './apiService';

export const AddUserPost = async (
  userId: number,
  content: any,
  images: any,
) => {
  try {
    const formData = new FormData();
    formData.append('userId', userId.toString());
    formData.append('content', content);

    images.forEach((image: any) => {
      formData.append('images', {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      });
    });
    const response = await apiClient.post('/posts/create', formData, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Error adding user post:', error);
    throw error.response?.data || error;
  }
};

// Alternative method using the new fetch-based API service
export const AddUserPostWithFetch = async (
  userId: number,
  content: any,
  images: any,
) => {
  try {
    const formData = new FormData();
    formData.append('userId', userId.toString());
    formData.append('content', content);

    images.forEach((image: any) => {
      formData.append('images', {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      });
    });

    // Using the new API service with automatic token handling
    return await apiService.uploadFile('posts/create', formData);
  } catch (error: any) {
    console.error('Error adding user post:', error);
    throw error;
  }
};

export const UpdateUserPost = async (
  userId: number,
  postId: number,
  postImagesIds: string,
  selectedImages: any[],
  content: string,
) => {
  try {
    const formData = new FormData();
    formData.append('userId', userId.toString());
    formData.append('postId', postId.toString());
    formData.append('imageIds', postImagesIds);
    formData.append('content', content);

    selectedImages.forEach((image: any) => {
      formData.append('images', {
        uri: image.uri,
        type: image.type,
        name: image.fileName,
      });
    });
    const response = await apiClient.put('/posts/update', formData, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error updating user post:', error);
    throw error.response?.data || error;
  }
};

export const deletePost = async (postId: number, userId: number) => {
  try {
    const response = await apiClient.delete(
      `posts/delete/${postId}/${userId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

// export const getPost = async () => {
//   try {
//     const response = await apiClient.get('/posts/list', {
//       headers: {
//         Accept: 'application/json',
//       },
//     });
//     return response.data;
//   } catch (error) {
//     if (error instanceof Error && 'response' in error) {
//       console.error((error as any).response?.data || error.message);
//     } else {
//       console.error(error);
//     }
//     throw error;
//   }
// };

export const getPost = async (page = 1, limit = 10) => {
  try {
    const response = await apiClient.get('/posts/list', {
      params: {page, limit},
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const createPost = async (payload: any) => {
  try {
    const response = await apiClient.post('/posts/create', payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const updatePost = async (payload: any) => {
  try {
    const response = await apiClient.put('/posts/update', payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const likePost = async (postId: number, userId: number) => {
  try {
    const response = await apiClient.post(`/posts/like/${postId}/${userId}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const unlikePost = async (postId: number, userId: number) => {
  try {
    const response = await apiClient.post(`/posts/unlike/${postId}/${userId}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const createComment = async (payload: any) => {
  try {
    const response = await apiClient.post('/posts/comment', payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const getPostComment = async (postId: number) => {
  try {
    const response = await apiClient.get(`posts/comments/${postId}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export const deleteComment = async (commentId: number, userId: number) => {
  try {
    const response = await apiClient.delete(
      `posts/comment/${commentId}/${userId}`,
      {
        headers: {
          Accept: 'application/json',
        },
      },
    );
    return response.data;
  } catch (error) {
    if (error instanceof Error && 'response' in error) {
      console.error((error as any).response?.data || error.message);
    } else {
      console.error(error);
    }
    throw error;
  }
};

export default {
  getPost,
  UpdateUserPost,
  AddUserPost,
  createPost,
  updatePost,
  likePost,
  unlikePost,
  createComment,
  deleteComment,
  getPostComment,
};
