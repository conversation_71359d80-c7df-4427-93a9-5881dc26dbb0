import {ScrollView, StyleSheet, Text, View, Image} from 'react-native';
import React from 'react';
import {SafeAreaView} from 'react-native-safe-area-context';
import AntDesign from 'react-native-vector-icons/AntDesign';

interface LikesProps {
  postData: any;
  likesCount: number;
}

const Likes: React.FC<LikesProps> = ({postData, likesCount}) => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.title}>
        {likesCount > 0 ? (
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            <Text style={{color: '#333333'}}> Post Likes</Text>
            <View style={{display: 'flex', flexDirection: 'row', right: 2}}>
              <View style={[styles.iconWrapper]}>
                <AntDesign name="like1" size={10} color="#ffffff" />
              </View>
              <Text style={{color: '#333333'}}> {likesCount}</Text>
            </View>
          </View>
        ) : (
          <></>
        )}
      </View>

      <ScrollView contentContainerStyle={styles.scrollView}>
        {postData && postData?.likedUsers.length > 0 ? (
          postData?.likedUsers.map((likePost: any) => (
            <View key={likePost.userId} style={styles.userCard}>
              <Image
                source={{uri: likePost.userImage}}
                style={styles.userImage}
              />
              <View style={styles.userInfo}>
                <Text style={styles.username}>{likePost?.name}</Text>
                {/* <Text style={styles.name}>{likePost?.username}</Text> */}
              </View>
            </View>
          ))
        ) : (
          <Text style={styles.noLikesText}>No likes for this post.</Text>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

export default Likes;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: 'medium',
    paddingVertical: 10,
  },
  scrollView: {
    paddingBottom: 16,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: '#ddd',
    borderRadius: 8,
  },
  userImage: {
    width: 25,
    height: 25,
    borderRadius: 25,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  username: {
    color: '#333333',
    fontSize: 15,
    fontWeight: 'medium',
  },
  name: {
    color: '#333333',
    fontSize: 14,
  },
  noLikesText: {
    color: '#333333',
    fontSize: 16,
    textAlign: 'center',
    marginTop: 20,
  },
  iconWrapper: {
    backgroundColor: '#0000FF',
    borderRadius: 99,
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
