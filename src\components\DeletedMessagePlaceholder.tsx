import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import normalize from '../types/utiles';
import Colors from '../constants/Colors';

interface DeletedMessagePlaceholderProps {
  deletedBy?: {
    userId: number;
    role: string;
    isAdmin: boolean;
  };
  deletedAt: string;
  type?: 'single' | 'bulk';
  bulkType?: 'bulk_delete_all' | 'bulk_delete_date_range' | 'bulk_delete_user' | 'bulk_delete_user_date_range';
}

const DeletedMessagePlaceholder: React.FC<DeletedMessagePlaceholderProps> = ({
  deletedBy,
  deletedAt,
  type = 'single',
  bulkType,
}) => {
  const getDeletedMessage = () => {
    if (!deletedBy?.isAdmin) {
      return 'This message was deleted';
    }

    if (type === 'bulk') {
      switch (bulkType) {
        case 'bulk_delete_all':
          return 'All messages were deleted by admin';
        case 'bulk_delete_date_range':
          return 'Messages in date range were deleted by admin';
        case 'bulk_delete_user':
          return 'User messages were deleted by admin';
        case 'bulk_delete_user_date_range':
          return 'User messages in date range were deleted by admin';
        default:
          return 'Messages were deleted by admin';
      }
    }

    return 'This message was deleted by admin';
  };

  const getDeletedTime = () => {
    const date = new Date(deletedAt);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <MaterialIcons 
          name="delete-outline" 
          size={normalize(16)} 
          color={Colors.textSecondary} 
          style={styles.icon}
        />
        <View style={styles.textContainer}>
          <Text style={styles.deletedText}>
            {getDeletedMessage()}
          </Text>
          <Text style={styles.timeText}>
            {getDeletedTime()}
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: normalize(4),
    marginHorizontal: normalize(10),
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: normalize(12),
    paddingVertical: normalize(8),
    borderRadius: normalize(8),
    borderLeftWidth: 3,
    borderLeftColor: '#dc3545',
  },
  icon: {
    marginRight: normalize(8),
  },
  textContainer: {
    flex: 1,
  },
  deletedText: {
    fontSize: normalize(13),
    color: Colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: normalize(2),
  },
  timeText: {
    fontSize: normalize(11),
    color: Colors.textTertiary,
  },
});

export default DeletedMessagePlaceholder;
