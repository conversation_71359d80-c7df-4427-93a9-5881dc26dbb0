import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from './PermissionGuard';

/**
 * Example component showing how to use ADD_EXPERIENCE and ADD_SKILLS permissions
 * This is a demonstration component - you can use these patterns in your actual screens
 */
const ExamplePermissionUsage: React.FC = () => {
  const { hasPermission, canAddExperience, canAddSkills } = usePermissions();

  const handleAddExperience = () => {
    console.log('Adding experience...');
    // Navigate to add experience screen or show modal
  };

  const handleAddSkills = () => {
    console.log('Adding skills...');
    // Navigate to add skills screen or show modal
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Profile Management</Text>

      {/* Method 1: Using PermissionGuard component */}
      <PermissionGuard permission="ADD_EXPERIENCE">
        <TouchableOpacity style={styles.button} onPress={handleAddExperience}>
          <Text style={styles.buttonText}>Add Experience</Text>
        </TouchableOpacity>
      </PermissionGuard>

      <PermissionGuard permission="ADD_SKILLS">
        <TouchableOpacity style={styles.button} onPress={handleAddSkills}>
          <Text style={styles.buttonText}>Add Skills</Text>
        </TouchableOpacity>
      </PermissionGuard>

      {/* Method 2: Using hook directly with conditional rendering */}
      {canAddExperience && (
        <TouchableOpacity style={styles.button} onPress={handleAddExperience}>
          <Text style={styles.buttonText}>Add Work Experience</Text>
        </TouchableOpacity>
      )}

      {canAddSkills && (
        <TouchableOpacity style={styles.button} onPress={handleAddSkills}>
          <Text style={styles.buttonText}>Add Technical Skills</Text>
        </TouchableOpacity>
      )}

      {/* Method 3: Using hasPermission function directly */}
      {hasPermission('ADD_EXPERIENCE') && (
        <TouchableOpacity style={styles.button} onPress={handleAddExperience}>
          <Text style={styles.buttonText}>Manage Experience</Text>
        </TouchableOpacity>
      )}

      {hasPermission('ADD_SKILLS') && (
        <TouchableOpacity style={styles.button} onPress={handleAddSkills}>
          <Text style={styles.buttonText}>Manage Skills</Text>
        </TouchableOpacity>
      )}

      {/* Show message if user doesn't have permissions */}
      {!canAddExperience && !canAddSkills && (
        <View style={styles.noPermissionContainer}>
          <Text style={styles.noPermissionText}>
            You don't have permission to add experience or skills.
          </Text>
          <Text style={styles.noPermissionSubtext}>
            Contact your administrator for access.
          </Text>
        </View>
      )}

      {/* Debug information */}
      <View style={styles.debugContainer}>
        <Text style={styles.debugTitle}>Permission Status:</Text>
        <Text style={styles.debugText}>
          ADD_EXPERIENCE: {canAddExperience ? '✅ Allowed' : '❌ Denied'}
        </Text>
        <Text style={styles.debugText}>
          ADD_SKILLS: {canAddSkills ? '✅ Allowed' : '❌ Denied'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 15,
    borderRadius: 8,
    marginVertical: 5,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  noPermissionContainer: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  noPermissionText: {
    fontSize: 16,
    color: '#856404',
    fontWeight: '600',
  },
  noPermissionSubtext: {
    fontSize: 14,
    color: '#856404',
    marginTop: 5,
  },
  debugContainer: {
    backgroundColor: '#e9ecef',
    padding: 15,
    borderRadius: 8,
    marginTop: 20,
  },
  debugTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#495057',
  },
  debugText: {
    fontSize: 14,
    color: '#6c757d',
    marginVertical: 2,
  },
});

export default ExamplePermissionUsage;
