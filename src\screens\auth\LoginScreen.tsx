import {
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import React, {useState} from 'react';
import Spacing from '../../constants/Spacing';
import FontSize from '../../constants/FontSize';
import Colors from '../../constants/Colors';
import Font from '../../constants/Font';
import AppTextInput from '../../components/AppTextInput';
import Ionicons from 'react-native-vector-icons/Ionicons';
import normalize from '../../types/utiles';
import {useDispatch, useSelector} from 'react-redux';
import {login, clearError} from '../../store/authSlice';
import {ALERT_TYPE, Dialog} from '../../components/CustomAlert';
import {debugTokens} from '../../utils/tokenDebug';
import AsyncStorage from '@react-native-async-storage/async-storage';

// type Props = NativeStackScreenProps<RootStackParamList, 'Login'>;
interface Props {
  navigation: any;
}
const LoginScreen: React.FC<Props> = ({navigation}) => {
  const [username, setUsername] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [isPasswordVisible, setPasswordVisible] = useState(false);

  const togglePasswordVisibility = () => {
    setPasswordVisible(!isPasswordVisible);
  };
  const dispatch = useDispatch();
  const {error, loading: authLoading} = useSelector(
    (state: any) => state.auth || {},
  );

  const handleLogin = async () => {
    setLoading(true);

    // Clear any previous errors
    dispatch(clearError());

    // Clear any existing tokens before login to start fresh
    console.log('Clearing existing tokens before login...');
    await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);

    // Debug tokens before login
    await debugTokens();

    try {
      // @ts-ignore
      const result = await dispatch(login({username, password}));

      if (login.rejected.match(result)) {
        const payload = result.payload as any;
        const errorMessage =
          payload?.message || 'Login failed. Please try again.';
        Dialog.show({
          type: ALERT_TYPE.DANGER,
          title: 'Login Failed',
          textBody: errorMessage,
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      }
    } catch (err) {
      console.error('Login error:', err);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'An unexpected error occurred. Please try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
    }
  };
  const {height} = Dimensions.get('window');

  return (
    <SafeAreaView>
      <View
        style={{
          // paddingTop: Spacing * 20,
          // flex: 1,
          // height: 'auto',
          height: height / 1.2,
          paddingLeft: Spacing * 2,
          paddingRight: Spacing * 2,
          display: 'flex',
          justifyContent: 'center',
        }}>
        <View
          style={{
            alignItems: 'center',
          }}>
          <Text
            style={{
              fontSize: normalize(20),
              fontWeight: 'bold',
              color: Colors.primary,
              marginVertical: normalize(10),
              fontFamily: Font['poppins-bold'],
            }}>
            Log in to your SGIC Alumini account
          </Text>
        </View>
        <View
          style={{
            marginVertical: Spacing * 5,
          }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: '#3795BD',
              backgroundColor: '#fff',
              borderRadius: normalize(5),
              paddingLeft: normalize(16),
              height: normalize(50),
              marginBottom: normalize(15),
            }}>
            <AppTextInput
              placeholder="Username"
              value={username}
              onChangeText={text => setUsername(text)}
              style={{
                flex: 1,
                color: '#000',
                fontSize: normalize(14),
                fontFamily: Font['poppins-bold'],
              }}
            />
          </View>

          {/* <AppTextInput
            placeholder="Password"
            value={password}
            onChangeText={text => setPassword(text)}
            secureTextEntry
          /> */}

          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              borderWidth: 1,
              borderColor: '#3795BD',
              backgroundColor: '#fff',
              borderRadius: normalize(5),
              paddingLeft: normalize(16),
              height: normalize(50),
            }}>
            <AppTextInput
              placeholder="Password"
              value={password}
              onChangeText={text => setPassword(text)}
              secureTextEntry={!isPasswordVisible}
              style={{
                flex: 1,
                color: '#000',
                fontSize: normalize(14),
                fontFamily: Font['poppins-bold'],
              }}
            />
            <TouchableOpacity
              onPress={togglePasswordVisibility}
              style={{
                position: 'absolute',
                right: normalize(16),
              }}>
              <Ionicons
                name={isPasswordVisible ? 'eye' : 'eye-off'}
                size={24}
                color="#000"
              />
            </TouchableOpacity>
          </View>
        </View>

        {error && (
          <Text
            style={{
              color: '#d81b1b',
              fontFamily: Font['poppins-semiBold'],
              textAlign: 'center',
              marginBottom: Spacing,
            }}>
            {error.message || 'An error occurred'}
          </Text>
        )}

        <View>
          <TouchableOpacity
            onPress={() => navigation.navigate('ForgotPassword')}
            style={{
              padding: 0,
              marginTop: 0,
              alignSelf: 'flex-end',
            }}>
            <Text
              style={{
                fontFamily: Font['poppins-regular'],
                color: Colors.primary,
                textAlign: 'center',
                fontSize: FontSize.medium,
                textDecorationLine: 'underline',
              }}>
              Forgot Password?
            </Text>
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          onPress={handleLogin}
          style={{
            padding: Spacing * 1,
            backgroundColor: Colors.primary,
            marginVertical: Spacing * 3,
            borderRadius: Spacing,
            shadowColor: Colors.primary,
            shadowOffset: {
              width: 0,
              height: Spacing,
            },
            shadowOpacity: 0.3,
            shadowRadius: Spacing,
          }}>
          {loading ? (
            <ActivityIndicator size="small" color={Colors.onPrimary} />
          ) : (
            <Text
              style={{
                fontFamily: Font['poppins-bold'],
                color: Colors.onPrimary,
                textAlign: 'center',
                fontSize: FontSize.large,
              }}>
              Login
            </Text>
          )}
          {/* <Text
            style={{
              fontFamily: Font['poppins-bold'],
              color: Colors.onPrimary,
              textAlign: 'center',
              fontSize: FontSize.large,
            }}>
            Sign in
          </Text> */}
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => navigation.navigate('Register')}
          style={{
            padding: Spacing,
          }}>
          <Text
            style={{
              fontFamily: Font['poppins-semiBold'],
              color: Colors.borderWithOpacity,
              textAlign: 'center',
              fontSize: FontSize.medium,
              textDecorationLine: 'underline',
            }}>
            Create new account
          </Text>
        </TouchableOpacity>

        {/* Forgot Password Link */}

        {/* Temporary Test Button - Remove after testing */}
        {/* <TouchableOpacity
          onPress={() =>
            navigation.navigate('ResetPassword', {
              token:
                '0c6ec490fda713e6813dfab0d117278336bbaa42ebed7dea9b0512b2ff2cb829',
            })
          }
          style={{
            padding: Spacing / 2,
            marginTop: Spacing / 2,
          }}>
          <Text
            style={{
              fontFamily: Font['poppins-regular'],
              color: '#ff6b6b',
              textAlign: 'center',
              fontSize: FontSize.small,
              textDecorationLine: 'underline',
            }}>
            Test Reset Password (Remove Later)
          </Text>
        </TouchableOpacity> */}

        {/* <View
          style={{
            marginVertical: Spacing * 3,
          }}>
          <Text
            style={{
              fontFamily: Font['poppins-semiBold'],
              color: Colors.primary,
              textAlign: 'center',
              fontSize: FontSize.small,
            }}>
            Or continue with
          </Text>

          <View
            style={{
              marginTop: Spacing,
              flexDirection: 'row',
              justifyContent: 'center',
            }}>
            <TouchableOpacity
              style={{
                padding: Spacing,
                backgroundColor: Colors.gray,
                borderRadius: Spacing / 2,
                marginHorizontal: Spacing,
              }}>
              <Image
                source={{
                  uri: 'https://w7.pngwing.com/pngs/249/19/png-transparent-google-logo-g-suite-google-guava-google-plus-company-text-logo.png',
                }}
                style={{
                  height: normalize(25),
                  width: normalize(25),
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                padding: Spacing,
                backgroundColor: Colors.gray,
                borderRadius: Spacing / 2,
                marginHorizontal: Spacing,
              }}>
              <Image
                source={{
                  uri: 'https://e7.pngegg.com/pngimages/670/159/png-clipart-facebook-logo-social-media-facebook-computer-icons-linkedin-logo-facebook-icon-media-internet.png',
                }}
                style={{
                  height: normalize(25),
                  width: normalize(25),
                }}
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>
        </View> */}
      </View>
    </SafeAreaView>
  );
};

export default LoginScreen;
