import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Share,
  Alert,
  Linking,
  ScrollView,
  Image,
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import moment from 'moment';

interface PostShareBottomSheetProps {
  postData: {
    id: number;
    authorName: string;
    authorImage: string;
    postText: string;
    postImages: string[];
    timeStamp: string;
  };
  onClose: () => void;
}

const PostShareBottomSheet: React.FC<PostShareBottomSheetProps> = ({
  postData,
  onClose,
}) => {
  const shareMessage = `📝 ${postData.authorName} shared a post:\n\n${postData.postText}${
    postData.postImages?.length > 0
      ? `\n\n📸 This post includes ${postData.postImages.length} image${
          postData.postImages.length > 1 ? 's' : ''
        }`
      : ''
  }\n\n🔗 Join our Alumni Network to see more posts and connect with fellow alumni!`;

  const handleGeneralShare = async () => {
    try {
      const result = await Share.share({
        message: shareMessage,
        title: `Post by ${postData.authorName}`,
      });
      if (result.action === Share.sharedAction) {
        console.log('Post shared successfully');
        onClose();
      }
    } catch (error) {
      console.error('Error sharing post:', error);
      Alert.alert('Error', 'Failed to share post. Please try again.');
    }
  };

  const shareToSocialMedia = async (platform: string) => {
    const message = encodeURIComponent(
      `📝 ${postData.authorName} shared: ${postData.postText}\n\n🔗 Join our Alumni Network!`,
    );
    let shareUrl = '';

    switch (platform) {
      case 'whatsapp':
        shareUrl = `whatsapp://send?text=${message}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?quote=${message}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${message}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?summary=${message}`;
        break;
      default:
        handleGeneralShare();
        return;
    }

    try {
      const supported = await Linking.canOpenURL(shareUrl);
      if (supported) {
        await Linking.openURL(shareUrl);
        onClose();
      } else {
        handleGeneralShare();
      }
    } catch (error) {
      console.error('Error opening social media app:', error);
      handleGeneralShare();
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Share Post</Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Ionicons name="close" size={20} color="#666" />
        </TouchableOpacity>
      </View>

      {/* Post Preview */}
      <View style={styles.postPreview}>
        <View style={styles.postHeader}>
          <Image
            source={{uri: postData.authorImage}}
            style={styles.authorImage}
          />
          <View style={styles.postInfo}>
            <Text style={styles.authorName}>{postData.authorName}</Text>
            <Text style={styles.postTime}>
              {moment(postData.timeStamp).fromNow()}
            </Text>
          </View>
        </View>
        
        <Text style={styles.postText} numberOfLines={2}>
          {postData.postText}
        </Text>
        
        {postData.postImages?.length > 0 && (
          <View style={styles.imageInfo}>
            <Ionicons name="image" size={14} color="#666" />
            <Text style={styles.imageText}>
              {postData.postImages.length} image{postData.postImages.length > 1 ? 's' : ''}
            </Text>
          </View>
        )}
      </View>

      {/* General Share */}
      <TouchableOpacity
        style={styles.shareButton}
        onPress={handleGeneralShare}>
        <View style={styles.shareButtonIcon}>
          <Ionicons name="share-outline" size={20} color="#fff" />
        </View>
        <View style={styles.shareButtonContent}>
          <Text style={styles.shareButtonText}>General Share</Text>
          <Text style={styles.shareButtonSubtext}>
            Share using any app on your device
          </Text>
        </View>
        <Ionicons name="chevron-forward" size={16} color="#ccc" />
      </TouchableOpacity>

      {/* Social Media Options */}
      <Text style={styles.socialTitle}>Share on Social Media</Text>
      <View style={styles.socialContainer}>
        <TouchableOpacity
          style={[styles.socialButton, {backgroundColor: '#25D366'}]}
          onPress={() => shareToSocialMedia('whatsapp')}>
          <Ionicons name="logo-whatsapp" size={24} color="#fff" />
          <Text style={styles.socialButtonText}>WhatsApp</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.socialButton, {backgroundColor: '#1877F2'}]}
          onPress={() => shareToSocialMedia('facebook')}>
          <Ionicons name="logo-facebook" size={24} color="#fff" />
          <Text style={styles.socialButtonText}>Facebook</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.socialButton, {backgroundColor: '#1DA1F2'}]}
          onPress={() => shareToSocialMedia('twitter')}>
          <Ionicons name="logo-twitter" size={24} color="#fff" />
          <Text style={styles.socialButtonText}>Twitter</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.socialButton, {backgroundColor: '#0A66C2'}]}
          onPress={() => shareToSocialMedia('linkedin')}>
          <Ionicons name="logo-linkedin" size={24} color="#fff" />
          <Text style={styles.socialButtonText}>LinkedIn</Text>
        </TouchableOpacity>
      </View>

      {/* Tips */}
      <View style={styles.tipsSection}>
        <Text style={styles.tipsTitle}>💡 Sharing Tips</Text>
        <Text style={styles.tipText}>
          • Add your own thoughts when sharing to make it more personal
        </Text>
        <Text style={styles.tipText}>
          • Tag fellow alumni to increase engagement
        </Text>
        <Text style={styles.tipText}>
          • Help grow our alumni community by sharing interesting posts
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 4,
  },
  postPreview: {
    backgroundColor: '#f8f9fa',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  authorImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  postInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  postTime: {
    fontSize: 11,
    color: '#666',
    marginTop: 1,
  },
  postText: {
    fontSize: 13,
    color: '#333',
    lineHeight: 18,
    marginBottom: 6,
  },
  imageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageText: {
    fontSize: 11,
    color: '#666',
    marginLeft: 4,
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  shareButtonIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#3795BD',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  shareButtonContent: {
    flex: 1,
  },
  shareButtonText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
  },
  shareButtonSubtext: {
    fontSize: 12,
    color: '#666',
    marginTop: 1,
  },
  socialTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 16,
    marginBottom: 12,
  },
  socialContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 20,
  },
  socialButton: {
    width: '48%',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  socialButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
  },
  tipsSection: {
    backgroundColor: '#f8f9fa',
    margin: 16,
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  tipsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 16,
    marginBottom: 4,
  },
});

export default PostShareBottomSheet;
