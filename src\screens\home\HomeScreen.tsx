import React, {useEffect, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Post from '../../components/Post';
import Ionicons from 'react-native-vector-icons/Ionicons';
import {NavigationProp, useNavigation} from '@react-navigation/native';
import {getPost} from '../../services/postServices';
import moment from 'moment';
import {useDispatch, useSelector} from 'react-redux';
import {fetchDetails} from '../../store/authSlice';
import normalize from '../../types/utiles';
import Colors from '../../constants/Colors';
import Font from '../../constants/Font';
import {GlobalStyles} from '../../styles/globalStyles';
import {TextStyles, Colors as FontColors} from '../../styles/fonts';
import PermissionGuard from '../../components/PermissionGuard';

type HomeScreenProps = {
  navigation: NavigationProp<any>;
  posts: any;
};
const HomeScreen: React.FC<HomeScreenProps> = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const [postList, setPostList] = useState<any>(null);
  const userId = useSelector((state: any) => state.auth.userId);
  const user = useSelector((state: any) => state.auth.userDetails);
  const userDetails = useSelector((state: any) => state.auth.userDetails);
  const dispatch = useDispatch();
  const [page, setPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isMoreLoading, setIsMoreLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isEndReached, setIsEndReached] = useState(false);

  const fetchPosts = async (page = 1, append = false) => {
    if (isLoading) return;
    setIsLoading(true);
    try {
      const res = await getPost(page);
      setPostList((prev: any) =>
        append ? [...prev, ...res.posts] : res.posts,
      );
      if (res.posts.length === 0) setIsEndReached(true);
    } catch (error) {
      console.error('Failed to fetch posts:', error);
    } finally {
      setIsLoading(false);
      setIsMoreLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchPosts(1);
    setPage(1);
    setIsRefreshing(false);
    setIsEndReached(false);
  };

  const handleLoadMore = () => {
    if (!isEndReached) {
      setPage(prevPage => prevPage + 1);
    }
  };

  useEffect(() => {
    if (postList === null) {
      setIsMoreLoading(true);
    } else {
      setIsMoreLoading(false);
    }
  }, [postList]);

  useEffect(() => {
    fetchPosts(page, page > 1);
  }, [page]);

  const onScrollHandler = ({nativeEvent}: any) => {
    const {layoutMeasurement, contentOffset, contentSize} = nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom && !isLoading) {
      handleLoadMore();
    }
  };

  useEffect(() => {
    if (userId) {
      // @ts-ignore
      dispatch(fetchDetails(userId));
    }
  }, [dispatch, userId]);

  return (
    <>
      {isMoreLoading ? (
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'white',
          }}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <ScrollView
          style={styles.container}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
            />
          }
          onScroll={onScrollHandler}
          scrollEventThrottle={16}>
          <SafeAreaView style={styles.container}>
            <View style={styles.header}>
              <View style={styles.searchContainer}>
                <View style={styles.searchBar}>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('Profile')}>
                    <Image
                      source={require('../../assets/images/company.png')}
                      style={styles.profilePicLogo}
                    />
                  </TouchableOpacity>
                </View>
                <View
                  style={{
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    alignItems: 'center',
                    gap: 10,
                  }}>
                  <PermissionGuard permission="ADD_POST">
                    <TouchableOpacity
                      onPress={() => navigation.navigate('AddPost')}>
                      <View
                        style={{
                          borderRadius: 6,
                          borderWidth: 1,
                          borderColor: '#ffffff',
                          padding: 2,
                          right: 24,
                        }}>
                        <Ionicons
                          name="add-outline"
                          size={18}
                          color={'#ffffff'}
                        />
                        {/* <Image
                          source={{
                            // uri: userDetails.image
                            uri:
                              userDetails?.image ||
                              'https://cdn-icons-png.flaticon.com/256/10238/10238174.png',
                          }}
                          style={styles.profilePic}
                        /> */}
                      </View>
                    </TouchableOpacity>
                  </PermissionGuard>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('Profile')}>
                    <Image
                      source={{
                        // uri: userDetails.image
                        uri:
                          userDetails?.image ||
                          'https://static.vecteezy.com/system/resources/previews/009/361/502/non_2x/3d-profile-realistic-icon-design-illustrations-3d-render-design-concept-vector.jpg',
                      }}
                      style={styles.profilePic}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <View>
              {postList && postList.length > 0 ? (
                postList.map((post: any) => (
                  <Post
                    key={post.id}
                    postId={post.id}
                    userId={post.userId}
                    likesCount={post.likesCount}
                    commentsCount={post.commentsCount}
                    companyLogo={post.userProfile}
                    companyName={post.name}
                    position={post.currentPosition}
                    timeStamp={moment(
                      `${post.date} ${post.time}`,
                      'YYYY-MM-DD hh:mm A',
                    ).fromNow()}
                    postText={post.content}
                    postImages={post.images.map((image: any) => image.url)}
                    postData={post}
                    fetchPosts={fetchPosts}
                  />
                ))
              ) : (
                <View
                  style={{
                    alignItems: 'center',
                    marginTop: normalize(20),
                  }}>
                  <View
                    style={{
                      width: 100,
                      height: 100,
                      borderRadius: normalize(100),
                      borderColor: '#000',
                      borderWidth: normalize(2),
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginBottom: normalize(10),
                    }}>
                    <Ionicons name="document-outline" size={50} color="#000" />
                  </View>
                  <Text
                    style={{
                      fontSize: normalize(14),
                      color: '#333',
                      textAlign: 'center',
                    }}>
                    No posts available.
                  </Text>
                </View>
              )}
              {isLoading && <ActivityIndicator size="large" color="#0000ff" />}
            </View>
          </SafeAreaView>
        </ScrollView>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: FontColors.background.secondary,
  },
  header: {
    backgroundColor: '#002157',
    paddingTop: 8,
    paddingBottom: 8,
    paddingHorizontal: 4,
    borderTopColor: '#ffffff',
    borderTopWidth: 0.5,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  profilePic: {
    width: 32,
    height: 32,
    borderRadius: 99,
    right: 8,
  },
  profilePicLogo: {
    width: '128%',
    height: 40,
    right: 35,
  },
  searchBar: {
    flex: 1,
    marginRight: 64,
    padding: 6,
    backgroundColor: '#002157',
    borderBottomRightRadius: 20,
    borderTopLeftRadius: 20,
    shadowColor: '#000000',
  },
  searchText: {
    ...TextStyles.h4,
    color: '#002157',
    letterSpacing: 1,
  },
  messageIcon: {
    width: 40,
    height: 40,
  },
  storySection: {
    flexDirection: 'row',
    marginTop: 12,
  },
  storyItem: {
    alignItems: 'center',
    marginRight: 12,
  },
  storyCircle: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#e4e6eb',
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyText: {
    ...TextStyles.caption,
    color: FontColors.text.secondary,
    marginTop: 4,
  },
});

export default HomeScreen;
