diff --git a/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt b/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt
index 1234567..abcdefg 100644
--- a/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt
+++ b/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerButtonViewManager.kt
@@ -37,7 +37,7 @@ import com.swmansion.gesturehandler.react.RNGestureHandlerButtonViewManager.But
 @ReactModule(name = RNGestureHandlerButtonViewManager.REACT_CLASS)
 class RNGestureHandlerButtonViewManager :
   ViewGroupManager<ButtonViewGroup>(),
-  ViewManagerWithGeneratedInterface<ButtonViewGroup> {
+  RNGestureHandlerButtonViewManagerInterface<ButtonViewGroup> {
 
   override fun getName() = REACT_CLASS
 
@@ -138,7 +138,7 @@ class RNGestureHandlerButtonViewManager :
     }
   }
 
-  override fun getDelegate(): ViewManagerDelegate<ButtonViewGroup> = delegate
+  // override fun getDelegate(): ViewManagerDelegate<ButtonViewGroup> = delegate
 
   companion object {
     const val REACT_CLASS = "RNGestureHandlerButton"
diff --git a/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt b/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt
index 1234567..abcdefg 100644
--- a/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt
+++ b/node_modules/react-native-gesture-handler/android/src/main/java/com/swmansion/gesturehandler/react/RNGestureHandlerRootViewManager.kt
@@ -12,7 +12,7 @@ import com.swmansion.gesturehandler.react.RNGestureHandlerRootViewManager.Gestu
 @ReactModule(name = RNGestureHandlerRootViewManager.REACT_CLASS)
 class RNGestureHandlerRootViewManager :
   ViewGroupManager<GestureHandlerRootViewGroup>(),
-  ViewManagerWithGeneratedInterface<GestureHandlerRootViewGroup> {
+  RNGestureHandlerRootViewManagerInterface<GestureHandlerRootViewGroup> {
 
   override fun getName() = REACT_CLASS
 
@@ -22,7 +22,7 @@ class RNGestureHandlerRootViewManager :
     return GestureHandlerRootViewGroup(reactContext)
   }
 
-  override fun getDelegate(): ViewManagerDelegate<GestureHandlerRootViewGroup> = delegate
+  // override fun getDelegate(): ViewManagerDelegate<GestureHandlerRootViewGroup> = delegate
 
   companion object {
     const val REACT_CLASS = "RNGestureHandlerRootView"
