import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  ImageBackground,
  StatusBar,
  ActivityIndicator,
  GestureResponderEvent,
  Dimensions,
} from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import Ionicons from 'react-native-vector-icons/Ionicons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Experience from '../components/Experience';
import Personal from '../components/Personal';
import authService, {
  getAllSkills,
  logoutUser,
  uploadTraineecoverImage,
  uploadTraineeImage,
} from '../services/authService';
import {useDispatch, useSelector} from 'react-redux';
import {NavigationProp} from '@react-navigation/native';
import {fetchDetails, logout} from '../store/authSlice';
import normalize from '../types/utiles';
import Experienceedit from '../components/Experienceedir';
import Skills from '../components/Skills';
import Colors from '../constants/Colors';
import {
  ImageLibraryOptions,
  ImagePickerResponse,
  launchImageLibrary,
} from 'react-native-image-picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Icon2 from 'react-native-vector-icons/AntDesign';
import {AppDispatch} from '../store/store';
import {ALERT_TYPE, Dialog} from '../components/CustomAlert';
import AntDesign from 'react-native-vector-icons/AntDesign';
import EvilIcons from 'react-native-vector-icons/EvilIcons';

import Font from '../constants/Font';
import {differenceInMonths, differenceInYears, format} from 'date-fns';
import {usePermissions} from '../hooks/usePermissions';
import PermissionGuard from '../components/PermissionGuard';
interface ExperienceItem {
  title: string;
  company: string;
  duration: string;
  stack: string;
  location: string;
}
interface Personal {
  name: string;
  address: string;
  email: string;
  nic: string;
}
interface SignUpFormData {
  employeeWorkId: any;
  jobTitle: string;
  stack: string;
  company: string;
  joiningDate: string;
  endDate: string;
  address: string;
}
interface PersonalFormData {
  name: string;
  email: string;
  nic: string;
  phoneNo: string;
  batchNo: string;
  address: string;
}
type profileprops = {
  navigation: NavigationProp<any>;
};

const ProfileScreen: React.FC<profileprops> = ({navigation: {navigate}}) => {
  const refRBSheet: any = useRef();
  const refRBSheet02: any = useRef();
  const [buttonType, setButtonType] = useState<string>('');
  const [experienceData, setExperienceData] = useState<any>([]);
  const [person, setPerson] = useState<any>([]);
  const token = useSelector((state: any) => state.auth.token);
  const userId = useSelector((state: any) => state.auth.userId);
  const [userInfo, setUserInfo] = useState<any>([]);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [selectExperience, setselectedExperience] = useState<any>();
  const dispatch = useDispatch<AppDispatch>();
  const [skills, setSkills] = useState<any>([]);
  const user = useSelector((state: any) => state.auth);
  const [loadingSkills, setLoadingSkills] = useState<boolean>(false);
  const [loadingDetails, setLoadingDetails] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState(false);

  // Permission hooks
  const {canAddExperience, canAddSkills} = usePermissions();
  const [iscoverLoading, setIscoverLoading] = useState(false);

  const fetchSkills = async () => {
    setLoadingSkills(true);
    try {
      const response = await getAllSkills(userId);
      setSkills(response.data);
      if (response.data.length > 0) setIsEdit(true);
    } catch (error) {
      console.error('Error fetching skills:', error);
    } finally {
      setLoadingSkills(false);
    }
  };

  const fetchUserDetails = async () => {
    setLoadingDetails(true);
    try {
      const res = await authService.getUserDetails(userId);
      setUserInfo(res?.data);
    } catch (error) {
      console.error('Error fetching user details:', error);
    } finally {
      setLoadingDetails(false);
    }
  };
  useEffect(() => {
    fetchUserDetails();
    fetchSkills();
  }, []);

  useEffect(() => {
    dispatch(fetchDetails(userId));
  }, [dispatch, userId]);

  const handleExperienceData = (data: SignUpFormData) => {
    setExperienceData((prevData: any) =>
      Array.isArray(prevData) ? [...prevData, data] : [data],
    );
  };
  const handleExperienceeditData = (updatedData: SignUpFormData) => {
    setExperienceData((prevData: any) => {
      return prevData.map((exp: any) => {
        // @ts-ignore
        if (exp.employeeWorkId === updatedData.id) {
          return {...exp, ...updatedData};
        }
        return exp;
      });
    });
  };

  const handlePersonalData = (dataPersonal: PersonalFormData) => {
    setPerson(dataPersonal);
  };
  const handlePress = (type: string) => {
    // Check permissions before opening modals
    if (type === 'experience' && !canAddExperience) {
      Dialog.show({
        type: ALERT_TYPE.WARNING,
        title: 'Permission Denied',
        textBody:
          "You don't have permission to add experience. Contact your administrator for access.",
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    if (type === 'skills' && !canAddSkills) {
      Dialog.show({
        type: ALERT_TYPE.WARNING,
        title: 'Permission Denied',
        textBody:
          "You don't have permission to add skills. Contact your administrator for access.",
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
      return;
    }

    setButtonType(type);
    refRBSheet?.current.open();
  };

  const handleEditExperience = (id: number, type: string) => {
    setButtonType(type);
    const selectedExperience = userInfo?.employmentHistory?.find(
      (exp: any) => exp.employeeWorkId === id,
    );
    if (selectedExperience) {
      setselectedExperience(selectedExperience);
    }
    refRBSheet?.current.open();
  };

  const formatDate = (date: any) => {
    if (!date) return 'Present';
    return format(new Date(date), 'dd MMM yyyy');
  };

  const calculateDuration = (startDate: any, endDate: any) => {
    if (!startDate) return '';
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    const years = differenceInYears(end, start);
    const months = differenceInMonths(end, start) % 12;

    let duration = '';
    if (years > 0) duration += `${years} yr${years > 1 ? 's' : ''}`;
    if (months > 0)
      duration += `${years > 0 ? ' ' : ''}${months} mo${months > 1 ? 's' : ''}`;
    return duration;
  };

  // Sort experiences by start date (most recent first)
  const sortedExperiences =
    userInfo?.employmentHistory?.sort((a: any, b: any) => {
      const dateA = new Date(a.startDate || 0);
      const dateB = new Date(b.startDate || 0);
      return dateB.getTime() - dateA.getTime();
    }) || [];

  const handleLogout = async () => {
    try {
      dispatch(logout(userId));
      Dialog.show({
        type: ALERT_TYPE.SUCCESS,
        title: 'Success',
        textBody: 'You have been logged out.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } catch (error: any) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Failed to log out!',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    }
  };

  const selectImage = async (
    userId: number,
    setUserInfo: (info: any) => void,
    userInfo: any,
    setLoading: (loading: boolean) => void,
  ) => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      quality: 1,
    };
    try {
      setLoading(true);

      const response: ImagePickerResponse = await new Promise(resolve => {
        launchImageLibrary(options, resolve);
      });

      if (response.didCancel) {
        setLoading(false);
        return;
      }

      if (response.errorMessage) {
        throw new Error(`ImagePicker Error: ${response.errorMessage}`);
      }

      if (response.assets && response.assets[0]) {
        const selectedImage = response.assets[0];

        if (selectedImage.uri) {
          setUserInfo({...userInfo, image: selectedImage.uri});

          await uploadTraineeImage(
            userId,
            selectedImage.uri,
            selectedImage.type || 'image/jpeg',
          );
          fetchUserDetails();
          Dialog.show({
            type: ALERT_TYPE.INFO,
            title: 'Success',
            textBody: 'Your Image has been updated successfully.',
            button: 'OK',
            onPressButton: () => Dialog.hide(),
          });
          setUserInfo({...userInfo, uploadedImage: selectedImage.uri});
        } else {
          console.error('Image URI is undefined');
        }
      }
    } catch (error) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Network Error',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
      setLoadingDetails(false);
    }
  };
  const selectcoverImage = async (
    userId: number,
    setUserInfo: (info: any) => void,
    userInfo: any,
    setcoverLoading: (coverloading: boolean) => void,
  ) => {
    const options: ImageLibraryOptions = {
      mediaType: 'photo',
      quality: 1,
    };
    try {
      setcoverLoading(true);

      const response: ImagePickerResponse = await new Promise(resolve => {
        launchImageLibrary(options, resolve);
      });

      if (response.didCancel) {
        setcoverLoading(false);
        return;
      }

      if (response.errorMessage) {
        throw new Error(`ImagePicker Error: ${response.errorMessage}`);
      }

      if (response.assets && response.assets[0]) {
        const selectedImage = response.assets[0];

        if (selectedImage.uri) {
          setUserInfo({...userInfo, coverImage: selectedImage.uri});

          await uploadTraineecoverImage(
            userId,
            selectedImage.uri,
            selectedImage.type || 'image/jpeg',
          );
          fetchUserDetails();
          Dialog.show({
            type: ALERT_TYPE.INFO,
            title: 'Success',
            textBody: 'Your Cover Image has been updated successfully.',
            button: 'OK',
            onPressButton: () => Dialog.hide(),
          });
          setUserInfo({...userInfo, uploadedImage: selectedImage.uri});
        } else {
          console.error('Image URI is undefined');
        }
      }
    } catch (error) {
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Network Error',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setcoverLoading(false);
      setLoadingDetails(false);
    }
  };

  const handleMorePress = () => {
    refRBSheet02.current?.open();
  };

  if (loadingSkills || loadingDetails) {
    return (
      <View style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }
  return (
    <>
      <SafeAreaView style={styles.container}>
        <ScrollView>
          <View>
            <View
              style={{
                flex: 1,
                justifyContent: 'flex-start',
              }}>
              {iscoverLoading ? (
                <ActivityIndicator
                  size="large"
                  color={Colors.primary}
                  style={styles.profileImageSection}
                />
              ) : (
                <ImageBackground
                  source={{
                    uri: userInfo.coverImage
                      ? userInfo.coverImage
                      : 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1737005087/LOGO_sa8pua.jpg',
                  }}
                  style={styles.profileImageSection}>
                  <TouchableOpacity
                    style={{
                      position: 'absolute',
                      bottom: normalize(1),
                      right: normalize(25),
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      borderRadius: normalize(15),
                      padding: normalize(5),
                    }}
                    onPress={() => {
                      selectcoverImage(
                        userId,
                        setUserInfo,
                        userInfo,
                        setIscoverLoading,
                      );
                    }}>
                    <Icon2 name="edit" size={20} color="#fff" />
                  </TouchableOpacity>
                </ImageBackground>
              )}
              <View style={styles.profileImageSectionOverlay}>
                {isLoading ? (
                  <ActivityIndicator
                    size="small"
                    color={Colors.primary}
                    style={{
                      marginLeft: normalize(20),
                      top: 120,
                    }}
                  />
                ) : (
                  <Image
                    source={{
                      uri: userInfo.image
                        ? userInfo.image
                        : 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1737005088/profile_oohrcd.png',
                    }}
                    style={styles.profileImage}
                  />
                )}
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    top: normalize(120),
                    left: normalize(55),
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    borderRadius: normalize(15),
                    padding: normalize(5),
                  }}
                  onPress={() =>
                    selectImage(userId, setUserInfo, userInfo, setIsLoading)
                  }>
                  <Icon name="camera-alt" size={20} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.profileInfo}>
              <View style={{display: 'flex', flexDirection: 'row'}}>
                <View>
                  <Text style={styles.name}>{userInfo?.name}</Text>
                  <Text style={styles.itemSubtitle}>
                    {userInfo?.currentPosition} at {userInfo?.currentCompany}
                  </Text>
                  <Text style={styles.itemSubtitle}>{userInfo?.batchNo}</Text>
                  {/* <Text style={styles.itemSubtitle}>{userInfo?.nic}</Text> */}
                  <Text style={styles.itemSubtitle}>{userInfo?.contactNo}</Text>
                  <Text style={styles.itemLocation}>{userInfo?.address}</Text>
                </View>
                <View
                  style={{
                    display: 'flex',
                    flex: 1,
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    marginRight: normalize(10),
                  }}>
                  <View
                    style={{
                      height: 32,
                      borderColor: 'black',
                      borderWidth: normalize(1),
                      borderRadius: 50,
                      padding: normalize(2),
                      justifyContent: 'center',
                      alignItems: 'center',
                    }}>
                    <Icon
                      name="more-horiz"
                      onPress={handleMorePress}
                      size={25}
                      color={'#524C42'}
                    />
                  </View>
                </View>
              </View>
              <View style={styles.locationContainer}></View>
            </View>
          </View>
          <View style={styles.actionButtons}></View>
          <View>
            <View style={styles.sectionHeader}>
              <View>
                <Text style={styles.sectionTitle}>Experience Timeline</Text>
              </View>

              <PermissionGuard permission="ADD_EXPERIENCE">
                <View>
                  <TouchableOpacity
                    style={styles.addExperienceButton}
                    onPress={() => handlePress('experience')}>
                    <Ionicons name="add-circle" size={24} color={'#0072b1'} />
                    <Text style={styles.addExperienceText}>Add</Text>
                  </TouchableOpacity>
                </View>
              </PermissionGuard>
            </View>
            {/* <View style={styles.headerActions}></View> */}
            {sortedExperiences && sortedExperiences.length > 0 ? (
              <View style={styles.timelineContainer}>
                {sortedExperiences.map((exp: any, index: number) => (
                  <View key={index} style={styles.timelineItem}>
                    {/* Timeline Line */}
                    <View style={styles.timelineLineContainer}>
                      <View style={styles.timelineDot} />
                      {index < sortedExperiences.length - 1 && (
                        <View style={styles.timelineLine} />
                      )}
                    </View>

                    {/* Experience Card */}
                    <View style={styles.experienceCard}>
                      <View style={styles.experienceHeader}>
                        <View style={styles.experienceMainInfo}>
                          <Text style={styles.companyName}>
                            {exp.companyName}
                          </Text>
                          <Text style={styles.positionTitle}>
                            {exp.position} ({exp.positionPrefix})
                          </Text>
                          <Text style={styles.stackInfo}>{exp.stack}</Text>
                        </View>
                        <TouchableOpacity
                          style={styles.editButton}
                          onPress={() =>
                            handleEditExperience(
                              exp.employeeWorkId,
                              'experienceedit',
                            )
                          }>
                          <EvilIcons
                            name="pencil"
                            size={20}
                            color={'#0072b1'}
                          />
                        </TouchableOpacity>
                      </View>

                      <View style={styles.experienceDetails}>
                        <View style={styles.dateContainer}>
                          <Ionicons
                            name="calendar-outline"
                            size={14}
                            color="#666"
                          />
                          <Text style={styles.dateText}>
                            {formatDate(exp.startDate)}-{' '}
                            {exp.workingPresent === 'No'
                              ? formatDate(exp.endDate)
                              : 'Present'}
                          </Text>
                        </View>
                        <View style={styles.durationContainer}>
                          <Ionicons
                            name="time-outline"
                            size={14}
                            color="#666"
                          />
                          <Text style={styles.durationText}>
                            {calculateDuration(exp.startDate, exp.endDate)}
                          </Text>
                        </View>
                        {exp.salaries && (
                          // <View style={styles.salaryContainer}>
                          //   <FontAwesome5
                          //     name="dollar-sign"
                          //     size={12}
                          //     color="#666"
                          //   />
                          //   <Text style={styles.salaryText}>
                          //     {exp.salaries}
                          //   </Text>
                          // </View>
                          <View style={styles.salaryContainer}>
                            <Ionicons
                              name="cash-outline"
                              size={16}
                              color="#666"
                            />
                            <Text style={styles.salaryText}>
                              {exp.salaries}
                            </Text>
                          </View>
                        )}
                      </View>

                      {/* Status Badge */}
                      <View style={styles.statusBadge}>
                        <Text
                          style={[
                            styles.statusText,
                            exp.endDate ? styles.pastJob : styles.currentJob,
                          ]}>
                          {exp.endDate ? 'Past' : 'Current'}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.emptyTimelineContainer}>
                <View style={styles.emptyIconContainer}>
                  <FontAwesome5 name="briefcase" size={48} color="#0072b1" />
                </View>
                <Text style={styles.emptyTimelineTitle}>
                  Start Your Career Timeline
                </Text>
                <Text style={styles.emptyTimelineText}>
                  Add your work experiences to build your professional timeline
                  and showcase your career journey
                </Text>
                <TouchableOpacity
                  style={styles.addFirstExperienceButton}
                  onPress={() => handlePress('experience')}>
                  <Ionicons name="add" size={20} color="#fff" />
                  <Text style={styles.addFirstExperienceText}>
                    Add Your First Experience
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          <View>
            <View style={styles.sectionHeader}>
              <View>
                <Text style={styles.sectionTitle}>Skills</Text>
              </View>

              <PermissionGuard permission="ADD_SKILLS">
                <View>
                  {skills && skills.length > 0 && (
                    <TouchableOpacity
                      style={styles.addExperienceButton}
                      onPress={() => handlePress('skills')}>
                      <Ionicons name="add-circle" size={24} color={'#0072b1'} />
                      <Text style={styles.addExperienceText}>Add</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </PermissionGuard>
            </View>
            {skills && skills.length > 0 ? (
              <View style={styles.itemContentSkills}>
                {skills.map((skill: any, index: number) => (
                  <Text key={index} style={styles.itemTitleSkills}>
                    {skill.skillName}
                  </Text>
                ))}
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>No skills added yet.</Text>
                <PermissionGuard permission="ADD_SKILLS">
                  <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => handlePress('skills')}>
                    <Text style={styles.addButtonText}>+ Add Skill</Text>
                  </TouchableOpacity>
                </PermissionGuard>
                {!canAddSkills && (
                  <Text style={styles.noPermissionText}>
                    Contact your administrator to add skills.
                  </Text>
                )}
              </View>
            )}
          </View>
          <RBSheet
            height={500}
            draggable
            ref={refRBSheet}
            useNativeDriver={false}
            closeOnPressMask={true}
            customStyles={{
              wrapper: {
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
              },
              container: {
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
              },
              draggableIcon: {
                backgroundColor: '#000',
                width: 40,
                height: 4,
              },
            }}
            customModalProps={{
              animationType: 'slide',
              statusBarTranslucent: true,
            }}
            customAvoidingViewProps={{
              enabled: false,
            }}>
            {buttonType === 'personal' ? (
              <Personal
                refRBSheet={refRBSheet}
                onSavePersonal={handlePersonalData}
                userInfo={userInfo}
                fetchUserDetails={fetchUserDetails}
              />
            ) : buttonType === 'experience' ? (
              <Experience
                refRBSheet={refRBSheet}
                onSave={handleExperienceData}
                userInfo={userInfo}
                fetchUserDetails={fetchUserDetails}
              />
            ) : buttonType === 'skills' ? (
              <Skills
                refRBSheet={refRBSheet}
                // onSave={handleSkillsData}
                userInfo={userInfo}
                isEdit={isEdit}
                fetchUserDetails={fetchSkills}
                userSkills={skills}
              />
            ) : (
              <Experienceedit
                refRBSheet={refRBSheet}
                onSave={handleExperienceeditData}
                userInfo={selectExperience}
                fetchUserDetails={fetchUserDetails}
              />
            )}
          </RBSheet>

          <RBSheet
            ref={refRBSheet02}
            height={200}
            draggable
            openDuration={250}
            closeOnPressMask={true}
            customStyles={{
              wrapper: {
                backgroundColor: 'rgba(0, 0, 0, 0.6)',
              },
              container: {
                borderTopLeftRadius: 20,
                borderTopRightRadius: 20,
                backgroundColor: '#F8F9FA',
                paddingHorizontal: 20,
                paddingTop: 10,
              },
              draggableIcon: {
                backgroundColor: '#A0A0A0',
                width: 40,
                height: 4,
              },
            }}
            customModalProps={{
              animationType: 'slide',
              statusBarTranslucent: true,
            }}
            customAvoidingViewProps={{
              enabled: true,
            }}>
            <TouchableOpacity
              onPress={() => {
                setButtonType('personal');
                refRBSheet.current.open();
                refRBSheet02.current.close();
              }}
              style={{
                paddingVertical: 14,
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#FFF',
                borderRadius: 12,
                marginBottom: 10,
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.1,
                shadowRadius: 4,
              }}
              activeOpacity={0.7}>
              <AntDesign name="edit" size={24} color="#007BFF" />
              <Text style={{fontSize: 18, color: '#333', marginLeft: 10}}>
                Edit
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                refRBSheet.current.close();
                navigate('AppShare');
              }}
              style={{
                paddingVertical: 14,
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#FFF',
                borderRadius: 12,
                marginBottom: 10,
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.1,
                shadowRadius: 4,
              }}
              activeOpacity={0.7}>
              <Ionicons name="share-social" size={24} color="#3795BD" />
              <Text style={{fontSize: 18, color: '#333', marginLeft: 10}}>
                Share App
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleLogout}
              style={{
                paddingVertical: 14,
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: '#FFF',
                borderRadius: 12,
                shadowColor: '#000',
                shadowOffset: {width: 0, height: 4},
                shadowOpacity: 0.1,
                shadowRadius: 4,
              }}
              activeOpacity={0.7}>
              <AntDesign name="logout" size={24} color="#28A745" />
              <Text style={{fontSize: 18, color: '#333', marginLeft: 10}}>
                Logout
              </Text>
            </TouchableOpacity>
          </RBSheet>
        </ScrollView>
      </SafeAreaView>
    </>
  );
};
const screenWidth = Dimensions.get('window').width;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  profileImage: {
    width: 90,
    height: 90,
    borderRadius: 99,
    marginLeft: 10,
    top: 90,
  },
  profileImageSection: {
    width: '105%',
    height: 130,
    objectFit: 'contain',
    paddingLeft: 10,
    resizeMode: 'center',
  },
  profileImageSectionOverlay: {
    position: 'absolute',
    alignItems: 'baseline',
  },
  profileInfo: {
    marginTop: 32,
    paddingLeft: 16,
    paddingTop: 28,
  },
  name: {
    fontSize: 22,
    fontWeight: 900,
    marginBottom: 4,
    fontFamily: Font['poppins-bold'],
    color: '#000',
  },
  title: {
    fontSize: 16,
    color: '#666',
    marginBottom: 4,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  location: {
    fontSize: 14,
    color: '#666',
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 4,
  },
  stats: {
    fontSize: 14,
    color: '#0072b1',
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 10,
    // borderBottomWidth: 1,
    borderBottomColor: '#e1e1e1',
  },
  personalButton: {
    backgroundColor: '#0072b1',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    marginRight: 8,
  },
  personalButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  messageButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#0072b1',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
  },
  messageButtonText: {
    color: '#0072b1',
    marginLeft: 4,
    fontWeight: '600',
  },
  moreButton: {
    borderWidth: 1,
    borderColor: '#666',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  // section: {
  //   // padding: 5,
  //   borderBottomWidth: 1,
  //   borderBottomColor: '#e1e1e1',
  // },
  // sectionTitle: {
  //   fontSize: 15, // Increased font size for more prominence
  //   fontWeight: 'bold', // Bolder text for a stronger heading
  //   color: '#333333', // Darker color for better readability
  //   marginVertical: 15, // Vertical spacing to create more breathing room
  //   textAlign: 'center', // Centering the text
  //   letterSpacing: 0 .5, // Slight spacing for clarity
  //   textTransform: 'uppercase', // Optional: uppercase text for added impact
  //   fontFamily: 'Arial', // Optional: different font for a cleaner look
  // },
  aboutText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  highlightsText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333',
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 8,
    borderTopWidth: 1,
    borderTopColor: '#e1e1e1',
    backgroundColor: '#fff',
  },
  navItem: {
    alignItems: 'center',
    flex: 1,
  },
  navText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },

  sectionHeader: {
    display: 'flex',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    width: '100%',
    paddingRight: 10,
    // backgroundColor: '#143D60',
  },
  sectionTitle: {
    display: 'flex',
    flex: 1,
    minWidth: screenWidth * 0.6,
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    paddingHorizontal: 10,
    paddingVertical: 5,
    letterSpacing: 1,
    backgroundColor: '#143D60',
    borderBottomRightRadius: 20,
    borderTopRightRadius: 20,
  },
  // buttonView: {
  //   display: 'flex',
  // },
  addExperienceButton: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f8ff',
    paddingHorizontal: 14,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0072b1',
    width: 'auto',
  },

  headerActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingHorizontal: 16,
  },
  iconButton: {
    // padding: 4,
  },
  experienceItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  educationItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  companyLogo: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 12,
  },
  institutionLogo: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 12,
  },
  itemContent: {
    flex: 1,
    paddingLeft: 5,
  },
  itemContentSkills: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 8,
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemTitleSkills: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 4,
    backgroundColor: '#ffffff',
    color: '#0072b1',
    paddingTop: 6,
    paddingBottom: 6,
    paddingLeft: 14,
    paddingRight: 14,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#0072b1',
  },
  itemSubtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 1,
  },
  itemDuration: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
    lineHeight: 20,
  },
  skillsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  skills: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  addButton: {
    backgroundColor: '#0072b1',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },

  // Timeline Styles

  addExperienceText: {
    color: '#0072b1',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  timelineContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  timelineItem: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  timelineLineContainer: {
    alignItems: 'center',
    marginRight: 16,
    width: 20,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#0072b1',
    borderWidth: 3,
    borderColor: '#fff',
    shadowColor: '#0072b1',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#e1e8ed',
    marginTop: 8,
  },
  experienceCard: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    borderLeftWidth: 3,
    borderLeftColor: '#0072b1',
  },
  experienceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  experienceMainInfo: {
    flex: 1,
  },
  companyName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  positionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0072b1',
    marginBottom: 4,
  },
  stackInfo: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
  },
  editButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#f0f8ff',
  },
  experienceDetails: {
    marginBottom: 12,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  dateText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    fontWeight: '500',
  },
  durationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  durationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    fontWeight: '500',
  },
  salaryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  salaryText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    fontWeight: '500',
  },
  statusBadge: {
    alignSelf: 'flex-start',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  currentJob: {
    backgroundColor: '#e8f5e8',
    color: '#2d7d32',
  },
  pastJob: {
    backgroundColor: '#f3f4f6',
    color: '#6b7280',
  },
  emptyTimelineContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f8ff',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  emptyTimelineTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyTimelineText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  addFirstExperienceButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#0072b1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#0072b1',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  addFirstExperienceText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  noPermissionText: {
    fontSize: 14,
    color: '#ff6b6b',
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
});

export default ProfileScreen;
