import apiClient from './axios';

import messaging, {firebase} from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const loginUser = async (credentials: {
  username: string;
  password: string;
}) => {
  try {
    console.log('Making login request with credentials:', {
      username: credentials.username,
      password: '***',
    });

    const response = await apiClient.post('/users/login', credentials, {
      headers: {
        Accept: 'application/json',
      },
    });

    console.log('Login response status:', response.status);
    console.log('Login response data keys:', Object.keys(response.data));

    const {userId: id, accessToken, refreshToken} = response.data;

    console.log('Extracted from response:', {
      userId: id,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      accessTokenLength: accessToken?.length,
      refreshTokenLength: refreshToken?.length,
    });

    if (!accessToken || !refreshToken) {
      throw new Error('Login response missing tokens');
    }

    console.log('Storing tokens in AsyncStorage...');
    await AsyncStorage.setItem('accessToken', accessToken);
    await AsyncStorage.setItem('refreshToken', refreshToken);
    const messagingSenderId: any = firebase.app().options.messagingSenderId;
    if (!messagingSenderId) {
      throw new Error('Firebase Messaging Sender ID is missing.');
    }

    const token = await messaging().getToken(messagingSenderId);

    const payload = {id, token};
    await createFcmToken(payload);

    return response.data;
  } catch (error: any) {
    console.error('Login Error:', error);

    throw error.response?.data || error;
  }
};

export const addUserExperience = async (userId: number, payload: any) => {
  try {
    const response = await apiClient.post(
      `/trainee/${userId}/company`,
      payload,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error adding experience:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const updateUserExperience = async (
  employeeWorkId: number,
  companyId: number,
  payload: any,
) => {
  try {
    const response = await apiClient.put(
      `/trainee/${employeeWorkId}/company/${companyId}/details`,
      payload,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('Error adding experience:', error);
    throw error.response?.data || error;
  }
};

export const addPersonDetails = async (userId: number, payload: any) => {
  try {
    const response = await apiClient.put(
      `/trainee/update-trainee/${userId}/`,
      payload,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error(
      'Error adding experience:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const uploadTraineeImage = async (
  userId: number,
  imageUri: string,
  contentType: string,
) => {
  try {
    const formData = new FormData();

    formData.append('image', {
      uri: imageUri,
      type: contentType,
      name: `image.${contentType.split('/')[1]}`,
    } as unknown as any);

    const response = await apiClient.put(
      `/trainee/update-trainee/${userId}`,
      formData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('Error uploading image:', error);
    throw error.response?.data || error;
  }
};

export const uploadTraineecoverImage = async (
  userId: number,
  imageUri: string,
  contentType: string,
) => {
  try {
    const formData = new FormData();

    formData.append('coverImage', {
      uri: imageUri,
      type: contentType,
      name: `image.${contentType.split('/')[1]}`,
    } as unknown as any);

    const response = await apiClient.put(
      `/trainee/update-trainee/${userId}`,
      formData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('Error uploading image:', error);
    throw error.response?.data || error;
  }
};

export const getUserDetails = async (userId: number) => {
  try {
    const response = await apiClient.get(`/trainee/a-trainee/${userId}`, {
      headers: {
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching user details:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const getAllSkills = async (userId: number) => {
  try {
    const response = await apiClient.get(`/skills/trainee/${userId}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching user details:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const addSkills = async (userId: number, payload: any) => {
  try {
    const response = await apiClient.post(`/skills/create/${userId}`, payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching user details:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const updateSkillById = async (userId: number, payload: any) => {
  try {
    const response = await apiClient.put(`/skills/update/${userId}`, payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching user details:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const getStacksPositions = async () => {
  try {
    const response = await apiClient.get('/trainee/stacks-positions', {
      headers: {
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching stacks positions:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const getSkills = async () => {
  try {
    const response = await apiClient.get('/trainee/skills', {
      headers: {
        Accept: 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching stacks positions:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const logoutUser = async (userId: number) => {
  try {
    const body = {
      userId: `${userId}`,
    };

    const response = await apiClient.post('/users/logout', body, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Logout Error:', error);
    throw error.response?.data || error;
  }
};

// Utility function to clear tokens and handle session expiration
export const clearUserSession = async () => {
  try {
    console.log('Clearing user session...');
    await AsyncStorage.multiRemove(['accessToken', 'refreshToken']);
    console.log('User session cleared');
  } catch (error) {
    console.error('Error clearing user session:', error);
  }
};

const createFcmToken = async (payload: any) => {
  try {
    const response = await apiClient.post(
      `/notification/create-fcm-user`,
      payload,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );

    return response.data;
  } catch (error: any) {
    console.error(
      'Error adding experience:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const getCountryList = async () => {
  try {
    const response = await apiClient.get('/country/list', {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data.data.country;
  } catch (error: any) {
    console.error(
      'Error fetching country list:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const getCitiesByCountry = async (countryId: string) => {
  try {
    const response = await apiClient.get(`/country/city/${countryId}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error fetching country list:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

export const createUserRegister = async (payload: any) => {
  try {
    const response = await apiClient.post('/trainee/create', payload, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error(
      'Error creating user registration:',
      error.response?.data || error.message,
    );
    throw error.response?.data || error;
  }
};

// Forgot Password - Send reset email
export const forgotPassword = async (email: string) => {
  try {
    const response = await apiClient.post(
      '/users/forgot-password',
      {email},
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('Forgot Password Error:', error);
    throw error.response?.data || error;
  }
};

// Reset Password - Update password with token
export const resetPassword = async (token: string, newPassword: string) => {
  try {
    const response = await apiClient.post(
      '/users/reset-password',
      {
        token,
        newPassword,
      },
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('Reset Password Error:', error);
    throw error.response?.data || error;
  }
};

// Verify Reset Token - Check if token is valid
export const verifyResetToken = async (token: string) => {
  try {
    const response = await apiClient.get(`/users/verify-reset-token/${token}`, {
      headers: {
        Accept: 'application/json',
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Verify Reset Token Error:', error);
    throw error.response?.data || error;
  }
};

// Get Latest App Share QR Code
export const getLatestAppShareQR = async () => {
  try {
    const response = await apiClient.get('/appShare/latest-qr', {
      headers: {
        Accept: 'application/json',
      },
    });
    return response;
  } catch (error: any) {
    console.error('Get Latest App Share QR Error:', error);
    throw error.response?.data || error;
  }
};

export default {
  loginUser,
  getUserDetails,
  logoutUser,
  addUserExperience,
  getStacksPositions,
  updateUserExperience,
  addPersonDetails,
  getCountryList,
  getCitiesByCountry,
  createUserRegister,
  forgotPassword,
  resetPassword,
  verifyResetToken,
  getLatestAppShareQR,
  getAllSkills,
  addSkills,
  updateSkillById,
  getSkills,
};
