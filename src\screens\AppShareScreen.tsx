import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  ActivityIndicator,
  Image,
  Share,
  Alert,
  ScrollView,
} from 'react-native';
import { NavigationProp } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import normalize from '../types/utiles';
import Font from '../constants/Font';
import { ALERT_TYPE, Dialog } from '../components/CustomAlert';
import authService from '../services/authService';

type Props = {
  navigation: NavigationProp<any>;
};

interface QRData {
  id: string;
  url: string;
  title: string;
  description: string;
  qrImageUrl: string;
  createdAt: string;
}

const AppShareScreen: React.FC<Props> = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [qrData, setQrData] = useState<QRData | null>(null);

  useEffect(() => {
    fetchLatestQR();
  }, []);

  const fetchLatestQR = async () => {
    try {
      setLoading(true);
      const response = await authService.getLatestAppShareQR();
      
      if (response.data.success) {
        setQrData(response.data.qrCode);
      } else {
        Dialog.show({
          type: ALERT_TYPE.INFO,
          title: 'No QR Code Available',
          textBody: 'No app sharing QR code has been generated yet. Please contact your administrator.',
          button: 'OK',
          onPressButton: () => Dialog.hide(),
        });
      }
    } catch (error: any) {
      console.error('Error fetching QR code:', error);
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: 'Failed to load QR code. Please try again.',
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    } finally {
      setLoading(false);
    }
  };

  const shareQRCode = async () => {
    if (!qrData) return;

    try {
      const shareOptions = {
        title: qrData.title,
        message: `${qrData.description}\n\nDownload the app: ${qrData.url}`,
        url: qrData.qrImageUrl,
      };

      await Share.share(shareOptions);
    } catch (error: any) {
      console.error('Error sharing QR code:', error);
      Alert.alert('Error', 'Failed to share QR code. Please try again.');
    }
  };

  const shareToSocialMedia = (platform: string) => {
    if (!qrData) return;

    const message = encodeURIComponent(`${qrData.description}\n\nDownload: ${qrData.url}`);
    let shareUrl = '';

    switch (platform) {
      case 'whatsapp':
        shareUrl = `whatsapp://send?text=${message}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(qrData.url)}&quote=${message}`;
        break;
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${message}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(qrData.url)}`;
        break;
      default:
        return;
    }

    // You can use Linking.openURL(shareUrl) here
    // For now, we'll use the general share function
    shareQRCode();
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor="#002157" />
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Share App</Text>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3795BD" />
          <Text style={styles.loadingText}>Loading QR Code...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#002157" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Ionicons name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Share App</Text>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={fetchLatestQR}>
          <Ionicons name="refresh" size={24} color="#fff" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {qrData ? (
          <>
            {/* QR Code Section */}
            <View style={styles.qrSection}>
              <View style={styles.qrContainer}>
                <Image
                  source={{ uri: qrData.qrImageUrl }}
                  style={styles.qrImage}
                  resizeMode="contain"
                />
              </View>
              
              <View style={styles.qrInfo}>
                <Text style={styles.qrTitle}>{qrData.title}</Text>
                <Text style={styles.qrDescription}>{qrData.description}</Text>
                <Text style={styles.qrUrl}>{qrData.url}</Text>
              </View>
            </View>

            {/* Share Options */}
            <View style={styles.shareSection}>
              <Text style={styles.sectionTitle}>Share Options</Text>
              
              {/* General Share */}
              <TouchableOpacity
                style={styles.shareButton}
                onPress={shareQRCode}>
                <Ionicons name="share-outline" size={24} color="#fff" />
                <Text style={styles.shareButtonText}>Share QR Code</Text>
              </TouchableOpacity>

              {/* Social Media Options */}
              <Text style={styles.socialTitle}>Share on Social Media</Text>
              <View style={styles.socialContainer}>
                <TouchableOpacity
                  style={[styles.socialButton, { backgroundColor: '#25D366' }]}
                  onPress={() => shareToSocialMedia('whatsapp')}>
                  <Ionicons name="logo-whatsapp" size={24} color="#fff" />
                  <Text style={styles.socialButtonText}>WhatsApp</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.socialButton, { backgroundColor: '#1877F2' }]}
                  onPress={() => shareToSocialMedia('facebook')}>
                  <Ionicons name="logo-facebook" size={24} color="#fff" />
                  <Text style={styles.socialButtonText}>Facebook</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.socialButton, { backgroundColor: '#1DA1F2' }]}
                  onPress={() => shareToSocialMedia('twitter')}>
                  <Ionicons name="logo-twitter" size={24} color="#fff" />
                  <Text style={styles.socialButtonText}>Twitter</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.socialButton, { backgroundColor: '#0A66C2' }]}
                  onPress={() => shareToSocialMedia('linkedin')}>
                  <Ionicons name="logo-linkedin" size={24} color="#fff" />
                  <Text style={styles.socialButtonText}>LinkedIn</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Instructions */}
            <View style={styles.instructionsSection}>
              <Text style={styles.sectionTitle}>How to Use</Text>
              <View style={styles.instructionItem}>
                <Ionicons name="scan-outline" size={20} color="#3795BD" />
                <Text style={styles.instructionText}>
                  Share this QR code with friends and family
                </Text>
              </View>
              <View style={styles.instructionItem}>
                <Ionicons name="camera-outline" size={20} color="#3795BD" />
                <Text style={styles.instructionText}>
                  They can scan it with their phone camera
                </Text>
              </View>
              <View style={styles.instructionItem}>
                <Ionicons name="download-outline" size={20} color="#3795BD" />
                <Text style={styles.instructionText}>
                  It will direct them to download the app
                </Text>
              </View>
            </View>
          </>
        ) : (
          <View style={styles.emptyState}>
            <Ionicons name="qr-code-outline" size={80} color="#ccc" />
            <Text style={styles.emptyTitle}>No QR Code Available</Text>
            <Text style={styles.emptyDescription}>
              No app sharing QR code has been generated yet. Please contact your administrator.
            </Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={fetchLatestQR}>
              <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#002157',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalize(16),
    paddingVertical: normalize(12),
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  backButton: {
    padding: normalize(8),
  },
  headerTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
    color: '#fff',
    fontFamily: Font['poppins-bold'],
  },
  headerRight: {
    width: normalize(40),
  },
  refreshButton: {
    padding: normalize(8),
  },
  content: {
    flex: 1,
    padding: normalize(20),
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: normalize(16),
    fontSize: normalize(16),
    color: '#666',
    fontFamily: Font['poppins-regular'],
  },
  qrSection: {
    backgroundColor: '#fff',
    borderRadius: normalize(16),
    padding: normalize(24),
    marginBottom: normalize(20),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    alignItems: 'center',
  },
  qrContainer: {
    backgroundColor: '#fff',
    padding: normalize(20),
    borderRadius: normalize(12),
    borderWidth: 2,
    borderColor: '#e9ecef',
    marginBottom: normalize(20),
  },
  qrImage: {
    width: normalize(250),
    height: normalize(250),
  },
  qrInfo: {
    alignItems: 'center',
  },
  qrTitle: {
    fontSize: normalize(20),
    fontWeight: '700',
    color: '#1a1a1a',
    fontFamily: Font['poppins-bold'],
    marginBottom: normalize(8),
    textAlign: 'center',
  },
  qrDescription: {
    fontSize: normalize(14),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
    lineHeight: normalize(20),
    marginBottom: normalize(12),
  },
  qrUrl: {
    fontSize: normalize(12),
    color: '#3795BD',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
  },
  shareSection: {
    backgroundColor: '#fff',
    borderRadius: normalize(16),
    padding: normalize(20),
    marginBottom: normalize(20),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  sectionTitle: {
    fontSize: normalize(18),
    fontWeight: '600',
    color: '#1a1a1a',
    fontFamily: Font['poppins-bold'],
    marginBottom: normalize(16),
  },
  shareButton: {
    backgroundColor: '#3795BD',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: normalize(14),
    paddingHorizontal: normalize(20),
    borderRadius: normalize(12),
    marginBottom: normalize(20),
    elevation: 2,
    shadowColor: '#3795BD',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  shareButtonText: {
    color: '#fff',
    fontSize: normalize(16),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
    marginLeft: normalize(8),
  },
  socialTitle: {
    fontSize: normalize(16),
    fontWeight: '500',
    color: '#1a1a1a',
    fontFamily: Font['poppins-bold'],
    marginBottom: normalize(12),
  },
  socialContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: normalize(10),
    paddingHorizontal: normalize(12),
    borderRadius: normalize(8),
    marginBottom: normalize(8),
    width: '48%',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  socialButtonText: {
    color: '#fff',
    fontSize: normalize(12),
    fontWeight: '500',
    fontFamily: Font['poppins-bold'],
    marginLeft: normalize(6),
  },
  instructionsSection: {
    backgroundColor: '#fff',
    borderRadius: normalize(16),
    padding: normalize(20),
    marginBottom: normalize(20),
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: normalize(12),
  },
  instructionText: {
    fontSize: normalize(14),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    marginLeft: normalize(12),
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: normalize(60),
  },
  emptyTitle: {
    fontSize: normalize(20),
    fontWeight: '600',
    color: '#1a1a1a',
    fontFamily: Font['poppins-bold'],
    marginTop: normalize(16),
    marginBottom: normalize(8),
  },
  emptyDescription: {
    fontSize: normalize(14),
    color: '#666',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
    lineHeight: normalize(20),
    marginBottom: normalize(24),
    paddingHorizontal: normalize(20),
  },
  retryButton: {
    backgroundColor: '#3795BD',
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(24),
    borderRadius: normalize(8),
  },
  retryButtonText: {
    color: '#fff',
    fontSize: normalize(14),
    fontWeight: '600',
    fontFamily: Font['poppins-bold'],
  },
});

export default AppShareScreen;
