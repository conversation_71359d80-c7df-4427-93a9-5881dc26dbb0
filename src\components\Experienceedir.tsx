import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Modal,
  FlatList,
} from 'react-native';
import {Calendar} from 'react-native-calendars';
import {
  getCitiesByCountry,
  getCountryList,
  getStacksPositions,
  updateUserExperience,
} from '../services/authService';
import {Picker} from '@react-native-picker/picker';
import DateTimePicker from '@react-native-community/datetimepicker';
import {ALERT_TYPE, Dialog} from './CustomAlert';
import Font from '../constants/Font';
interface SignUpFormData {
  country: string;
  countryId: string;
  countryName: string;
  city: string;
  cityId: string;
  cityName: string;
  street: string;
  startDate: any;
  workingPresent: string;
  jobTitle: string;
  stack: string;
  company: string;
  joiningDate: string;
  endDate: string;
  address: string;
  companyName: string;
  companyAddress: string;
  positionId: number;
  position: string;
  stackId: number;
  salaryId: number;
  salaries: string;
}
interface ExperienceeditProps {
  refRBSheet: any;
  userInfo: any;
  onSave?: any;
  fetchUserDetails: any;
}

const Experienceedit: React.FC<ExperienceeditProps> = ({
  refRBSheet,
  onSave,
  userInfo,
  fetchUserDetails,
}) => {
  const [formData, setFormData] = useState<SignUpFormData>(userInfo);
  const [showJoiningDatePicker, setShowJoiningDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);
  const [stacksPositions, setStacksPositions] = useState<any>([]);
  const [countries, setCountries] = useState<any[]>([]);
  const [filteredCountries, setFilteredCountries] = useState<any[]>([]);
  const [cities, setCities] = useState<any[]>([]);
  const [filteredCities, setFilteredCities] = useState<any[]>([]);
  const [countrySearchText, setCountrySearchText] = useState('');
  const [citySearchText, setCitySearchText] = useState('');
  const [showCountryModal, setShowCountryModal] = useState(false);
  const [showCityModal, setShowCityModal] = useState(false);

  useEffect(() => {
    const fetchStacksPositions = async () => {
      try {
        const response = await getStacksPositions();
        setStacksPositions(response?.data);
      } catch (error) {
        console.log(error);
      }
    };
    fetchStacksPositions();
  }, []);

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const countryList = await getCountryList();
        setCountries(countryList || []);
        setFilteredCountries(countryList || []);
      } catch (error) {
        console.error(error);
      }
    };
    fetchCountries();
  }, []);

  // Load cities when component mounts if country is already selected (edit mode)
  useEffect(() => {
    const loadCitiesForSelectedCountry = async () => {
      if (formData.countryId || formData.country) {
        try {
          const countryId = formData.countryId || formData.country;
          const citiesData = await getCitiesByCountry(countryId);
          const citiesArray = citiesData?.data || [];
          setCities(citiesArray);
          setFilteredCities(citiesArray);
        } catch (error) {
          console.error('Error loading cities for selected country:', error);
        }
      }
    };
    loadCitiesForSelectedCountry();
  }, [formData.countryId, formData.country]);

  const handleCountryChange = async (countryId: string) => {
    setFormData({
      ...formData,
      country: countryId,
      city: '',
      companyAddress: '',
    });
    try {
      const citiesData = await getCitiesByCountry(countryId);
      const cities = citiesData?.data || [];
      setCities(cities || []);
      setFilteredCities(cities || []);
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };
  const handleCityChange = (cityId: number) => {
    const selectedCity = filteredCities.find(city => city.id === cityId);
    //@ts-ignore
    setFormData(prevData => ({
      ...prevData,
      cityId,
      cityName: selectedCity ? selectedCity.name : '',
    }));
  };

  const handleCountrySearch = (searchText: string) => {
    setCountrySearchText(searchText);
    if (searchText.trim() === '') {
      setFilteredCountries(countries);
    } else {
      const filtered = countries.filter((country: any) =>
        country.name.toLowerCase().includes(searchText.toLowerCase()),
      );
      setFilteredCountries(filtered);
    }
  };

  const handleCitySearch = (searchText: string) => {
    setCitySearchText(searchText);
    if (searchText.trim() === '') {
      setFilteredCities(cities);
    } else {
      const filtered = cities.filter((city: any) =>
        city.name.toLowerCase().includes(searchText.toLowerCase()),
      );
      setFilteredCities(filtered);
    }
  };

  const selectCountry = async (country: any) => {
    setFormData(prevData => ({
      ...prevData,
      country: country.id,
      countryId: country.id,
      countryName: country.name,
      city: '',
      cityId: '',
      cityName: '',
      companyAddress: '',
    }));
    setShowCountryModal(false);
    setCountrySearchText('');
    setFilteredCountries(countries);

    try {
      const citiesData = await getCitiesByCountry(country.id);
      const citiesArray = citiesData?.data || [];
      setCities(citiesArray);
      setFilteredCities(citiesArray);
    } catch (error) {
      console.error('Error fetching cities:', error);
      setCities([]);
      setFilteredCities([]);
    }
  };

  const selectCity = (city: any) => {
    setFormData(prevData => ({
      ...prevData,
      city: city.id,
      cityId: city.id,
      cityName: city.name,
    }));
    setShowCityModal(false);
    setCitySearchText('');
    setFilteredCities(cities);
  };

  const handleSignUp = async () => {
    const {employeeWorkId, companyId} = userInfo;
    const payload = {
      companyName: formData.companyName,
      companyAddress: formData.companyAddress,
      cityId: formData.cityId,
      positionId: formData.positionId,
      stackId: formData.stackId,
      startDate: formData.startDate,
      workingPresent: formData.workingPresent,
      endDate: formData.workingPresent === 'Yes' ? null : formData.endDate,
      experience: '',
      salaryId: formData.salaryId,
    };
    try {
      const response = await updateUserExperience(
        employeeWorkId,
        companyId,
        payload,
      );
      if (onSave) {
        onSave(formData);
      }
      if (refRBSheet?.current?.close) {
        Dialog.show({
          type: ALERT_TYPE.INFO,
          title: 'Experice updated successfully!',
          button: 'OK',
          onPressButton: () => {
            Dialog.hide();
            if (refRBSheet?.current?.close) {
              refRBSheet.current.close();
            }
          },
        });
      }

      fetchUserDetails();
    } catch (error: any) {
      console.error('Failed to add experience:', error);
      const errorMessage =
        error?.message ||
        'An error occurred while adding the experience. Please try again later.';
      Dialog.show({
        type: ALERT_TYPE.DANGER,
        title: 'Error',
        textBody: errorMessage,
        button: 'OK',
        onPressButton: () => Dialog.hide(),
      });
    }
  };
  const formatDate = (dateString: any) => {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${year}/${month}/${day}`;
  };

  const handleDateChange = (
    event: any,
    selectedDate: Date | undefined,
    field: string,
  ) => {
    if (selectedDate) {
      const formattedDate = formatDate(selectedDate);
      setFormData({...formData, [field]: formattedDate});
    }

    if (field === 'startDate') {
      setShowJoiningDatePicker(false);
    } else if (field === 'endDate') {
      setShowEndDatePicker(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Edit Experience</Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Company Name</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g. Invicta Innovations (Pvt.) Ltd."
              placeholderTextColor="#999"
              value={formData.companyName}
              onChangeText={text =>
                setFormData({...formData, companyName: text})
              }
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Job Title</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.position}
                onValueChange={(itemValue: any) => {
                  const selectedCompany = stacksPositions?.position?.find(
                    (position: any) => position.id === itemValue,
                  );
                  setFormData({
                    ...formData,
                    positionId: itemValue,
                    position: selectedCompany?.title,
                  });
                }}
                style={{
                  color: '#000000',
                  bottom: 4,
                }}>
                <Picker.Item
                  label={formData.position}
                  value={formData.position}
                />
                {stacksPositions?.position?.map(
                  (position: any, index: number) => (
                    <Picker.Item
                      key={index}
                      label={position.title}
                      value={position.id}
                    />
                  ),
                )}
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Salary Range</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.salaryId}
                onValueChange={(itemValue: any) => {
                  const selectedSalary = stacksPositions?.salary?.find(
                    (salary: any) => salary.id === itemValue,
                  );
                  setFormData({
                    ...formData,
                    salaryId: selectedSalary?.id,
                    salaries: selectedSalary?.name,
                  });
                }}
                style={{
                  color: '#000000',
                  bottom: 2,
                }}>
                <Picker.Item
                  label={formData.salaries}
                  value={formData.salaryId}
                  style={{color: '#a18d8d'}}
                />
                {stacksPositions?.salary?.map((salary: any, index: number) => (
                  <Picker.Item
                    key={index}
                    label={salary.name}
                    value={salary.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Stack</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.stackId}
                onValueChange={(itemValue: any) => {
                  const selectedStack = stacksPositions?.stack?.find(
                    (stack: any) => stack.id === itemValue,
                  );
                  setFormData({...formData, stack: selectedStack?.id});
                }}
                style={{
                  color: '#000000',
                  bottom: 4,
                }}>
                <Picker.Item label={formData.stack} value={formData.stackId} />
                {stacksPositions?.stack?.map((stack: any, index: any) => (
                  <Picker.Item
                    key={index}
                    label={stack.name}
                    value={stack.id}
                  />
                ))}
              </Picker>
            </View>
          </View>

          {/* Joining Date */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Joining Date</Text>
            <TouchableOpacity
              style={styles.input}
              onPress={() => setShowJoiningDatePicker(true)}>
              <Text style={{color: formData.startDate ? '#000' : '#999'}}>
                {formData.startDate || 'Select Date'}
              </Text>
            </TouchableOpacity>
            {showJoiningDatePicker && (
              <DateTimePicker
                value={
                  formData.startDate ? new Date(formData.startDate) : new Date()
                }
                mode="date"
                display="default"
                onChange={(event: any, date: any) =>
                  handleDateChange(event, date, 'startDate')
                }
              />
            )}
          </View>
          {/* Working present */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Working Present</Text>
            <View style={styles.input}>
              <Picker
                selectedValue={formData.workingPresent}
                onValueChange={(itemValue: any) => {
                  setFormData({...formData, workingPresent: itemValue});
                }}
                style={{
                  color: '#000000',
                  bottom: 4,
                }}>
                <Picker.Item label="Select" value="" />
                <Picker.Item label="Yes" value="Yes" />
                <Picker.Item label="No" value="No" />
              </Picker>
            </View>
          </View>

          {/* End Date */}
          <View style={styles.inputContainer}>
            <Text style={styles.label}>End Date</Text>
            <TouchableOpacity
              style={styles.input}
              disabled={formData.workingPresent === 'Yes'}
              onPress={() => setShowEndDatePicker(true)}>
              <Text style={{color: formData.endDate ? '#000' : '#999'}}>
                {formData.endDate || 'Select Date'}
              </Text>
            </TouchableOpacity>
            {showEndDatePicker && (
              <DateTimePicker
                value={
                  formData.endDate ? new Date(formData.endDate) : new Date()
                }
                mode="date"
                display="default"
                onChange={(event, date) =>
                  handleDateChange(event, date, 'endDate')
                }
              />
            )}
          </View>

          {/* <View style={styles.inputContainer}>
            <Text style={styles.label}>Address</Text>
            <TextInput
              style={styles.input}
              placeholder="Jaffna District, Northern Province, Sri Lanka."
              placeholderTextColor="#999"
              value={formData.companyAddress}
              onChangeText={text =>
                setFormData({ ...formData, companyAddress: text })
              }
            />
          </View> */}

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Country</Text>
            <TouchableOpacity
              style={styles.pickerButton}
              onPress={() => setShowCountryModal(true)}>
              <Text style={styles.pickerButtonText}>
                {formData.countryName || 'Select Country'}
              </Text>
              <Text style={styles.pickerArrow}>▼</Text>
            </TouchableOpacity>
          </View>

          {/* City Selection */}
          {formData.countryName && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>City</Text>
              <TouchableOpacity
                style={styles.pickerButton}
                onPress={() => {
                  console.log(
                    'Opening city modal, cities available:',
                    cities.length,
                  );
                  console.log('Filtered cities:', filteredCities.length);
                  setShowCityModal(true);
                }}>
                <Text style={styles.pickerButtonText}>
                  {formData.cityName || 'Select City'}
                </Text>
                <Text style={styles.pickerArrow}>▼</Text>
              </TouchableOpacity>
            </View>
          )}

          {/* Street Selection */}
          {formData.cityName && (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Street</Text>
              <TextInput
                style={styles.input}
                placeholder="e.g. Northern Street"
                placeholderTextColor="#999"
                value={formData.companyAddress}
                onChangeText={text =>
                  setFormData({...formData, companyAddress: text})
                }
              />
              {/* <Picker
                selectedValue={formData.street}
                style={styles.picker}
                onValueChange={(itemValue) => setFormData({ ...formData, street: itemValue })}
              >
                <Picker.Item label="Select Street" value="" />
                {streets.map((street, index) => (
                  <Picker.Item key={index} label={street.name} value={street.id} />
                ))}
              </Picker> */}
            </View>
          )}
        </View>

        <TouchableOpacity style={styles.signUpButton} onPress={handleSignUp}>
          <Text style={styles.signUpButtonText}>Edit</Text>
        </TouchableOpacity>
      </SafeAreaView>

      {/* Country Modal */}
      <Modal
        visible={showCountryModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCountryModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity
                onPress={() => setShowCountryModal(false)}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search countries..."
              placeholderTextColor="#999"
              value={countrySearchText}
              onChangeText={handleCountrySearch}
            />
            <FlatList
              data={filteredCountries}
              keyExtractor={item => item.id.toString()}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => selectCountry(item)}>
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>

      {/* City Modal */}
      <Modal
        visible={showCityModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCityModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select City</Text>
              <TouchableOpacity
                onPress={() => setShowCityModal(false)}
                style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.searchInput}
              placeholder="Search cities..."
              placeholderTextColor="#999"
              value={citySearchText}
              onChangeText={handleCitySearch}
            />
            <FlatList
              data={filteredCities}
              keyExtractor={item => item.id.toString()}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => selectCity(item)}>
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingBottom: 8,
    color: '#FFFFFF',
    marginBottom: 12,
    backgroundColor: '#002157',
  },
  backButton: {
    padding: 8,
  },
  backArrow: {
    fontSize: 24,
    color: '#FF3B30',
  },
  title: {
    marginTop: 12,
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
    fontFamily: Font['poppins-regular'],
    textAlign: 'center',
  },
  form: {
    paddingHorizontal: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#000000',
  },
  input: {
    height: 48,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
    justifyContent: 'center',
    color: '#333',
  },
  signUpButton: {
    backgroundColor: '#007CBC',
    marginHorizontal: 16,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 24,
  },
  signUpButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  termsText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginHorizontal: 16,
    marginTop: 16,
  },
  link: {
    color: '#FF3B30',
  },
  pickerButton: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd6d6',
    borderRadius: 8,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#fff',
  },
  pickerButtonText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  pickerArrow: {
    fontSize: 12,
    color: '#666',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: '#666',
  },
  searchInput: {
    height: 48,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 16,
    marginBottom: 16,
    fontSize: 16,
  },
  listItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listItemText: {
    fontSize: 16,
    color: '#333',
  },
});

export default Experienceedit;
