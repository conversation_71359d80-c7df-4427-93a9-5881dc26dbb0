import {Platform} from 'react-native';

// LinkedIn-inspired font configuration
export const FontFamily = {
  // Primary font family (LinkedIn Sans equivalent)
  regular: Platform.select({
    ios: 'System', // San Francisco on iOS
    android: 'Roboto', // Roboto on Android
    default: 'System',
  }),
  medium: Platform.select({
    ios: 'System', // San Francisco Medium
    android: 'Roboto-Medium',
    default: 'System',
  }),
  semiBold: Platform.select({
    ios: 'System', // San Francisco Semibold
    android: 'Roboto-Medium', // Closest to semibold on Android
    default: 'System',
  }),
  bold: Platform.select({
    ios: 'System', // San Francisco Bold
    android: 'Roboto-Bold',
    default: 'System',
  }),
};

// LinkedIn-inspired font weights
export const FontWeight = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semiBold: '600' as const,
  bold: '700' as const,
};

// LinkedIn-inspired font sizes
export const FontSize = {
  // Headings
  h1: 28,
  h2: 24,
  h3: 20,
  h4: 18,
  h5: 16,
  h6: 14,
  
  // Body text
  large: 16,
  medium: 14,
  small: 12,
  xSmall: 10,
  
  // UI elements
  button: 14,
  caption: 11,
  overline: 10,
};

// LinkedIn-inspired line heights
export const LineHeight = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
};

// LinkedIn-inspired letter spacing
export const LetterSpacing = {
  tight: -0.5,
  normal: 0,
  wide: 0.5,
  wider: 1,
};

// Pre-defined text styles matching LinkedIn's design
export const TextStyles = {
  // Headers
  h1: {
    fontFamily: FontFamily.bold,
    fontSize: FontSize.h1,
    fontWeight: FontWeight.bold,
    lineHeight: FontSize.h1 * LineHeight.tight,
    letterSpacing: LetterSpacing.tight,
  },
  h2: {
    fontFamily: FontFamily.bold,
    fontSize: FontSize.h2,
    fontWeight: FontWeight.bold,
    lineHeight: FontSize.h2 * LineHeight.tight,
    letterSpacing: LetterSpacing.tight,
  },
  h3: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSize.h3,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.h3 * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  h4: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSize.h4,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.h4 * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  h5: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.h5,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.h5 * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  h6: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.h6,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.h6 * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // Body text
  bodyLarge: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.large,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.large * LineHeight.relaxed,
    letterSpacing: LetterSpacing.normal,
  },
  bodyMedium: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.medium,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.medium * LineHeight.relaxed,
    letterSpacing: LetterSpacing.normal,
  },
  bodySmall: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.small,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.small * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // UI elements
  button: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSize.button,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.button * LineHeight.normal,
    letterSpacing: LetterSpacing.wide,
  },
  caption: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.caption,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.caption * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  overline: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.overline,
    fontWeight: FontWeight.medium,
    lineHeight: FontSize.overline * LineHeight.normal,
    letterSpacing: LetterSpacing.wider,
    textTransform: 'uppercase' as const,
  },
  
  // Special styles
  username: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSize.medium,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.medium * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  timestamp: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.small,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.small * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  postContent: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.medium,
    fontWeight: FontWeight.regular,
    lineHeight: FontSize.medium * LineHeight.relaxed,
    letterSpacing: LetterSpacing.normal,
  },
  navigationTitle: {
    fontFamily: FontFamily.semiBold,
    fontSize: FontSize.h5,
    fontWeight: FontWeight.semiBold,
    lineHeight: FontSize.h5 * LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
};

// Color scheme to complement LinkedIn's typography
export const Colors = {
  text: {
    primary: '#000000',
    secondary: '#666666',
    tertiary: '#999999',
    inverse: '#FFFFFF',
    link: '#0A66C2', // LinkedIn blue
    success: '#057642',
    warning: '#B24020',
    error: '#CC1016',
  },
  background: {
    primary: '#FFFFFF',
    secondary: '#F3F2EF',
    tertiary: '#E9E5DF',
  },
};
