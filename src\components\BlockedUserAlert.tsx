import React, { useEffect, useState } from 'react';
import { Alert, Modal, StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { socket } from '../services/socket';

interface BlockedUserAlertProps {
  visible: boolean;
  onClose: () => void;
  groupName?: string;
  message?: string;
}

const BlockedUserAlert: React.FC<BlockedUserAlertProps> = ({
  visible,
  onClose,
  groupName,
  message,
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.alertContainer}>
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>🚫</Text>
          </View>
          
          <Text style={styles.title}>Access Blocked</Text>
          
          <Text style={styles.message}>
            {message || `You have been blocked from sending messages in "${groupName || 'this group'}" by an admin.`}
          </Text>
          
          <TouchableOpacity style={styles.button} onPress={onClose}>
            <Text style={styles.buttonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

// Hook for managing blocked user alerts
export const useBlockedUserAlert = () => {
  const [alertVisible, setAlertVisible] = useState(false);
  const [alertData, setAlertData] = useState<{
    groupName?: string;
    message?: string;
  }>({});
  
  const userId = useSelector((state: any) => state.auth.userId);
  const isAuthenticated = useSelector((state: any) => state.auth.isAuthenticated);

  useEffect(() => {
    if (!isAuthenticated || !userId) return;

    console.log('🚫 Setting up blocked user alert listener for user:', userId);

    // Listen for block notifications
    const handleMemberBlocked = (data: any) => {
      console.log('🚫 Received member blocked notification:', data);
      
      setAlertData({
        groupName: data.groupName,
        message: data.message,
      });
      setAlertVisible(true);
    };

    socket.on('memberBlocked', handleMemberBlocked);

    return () => {
      socket.off('memberBlocked', handleMemberBlocked);
    };
  }, [isAuthenticated, userId]);

  const showBlockAlert = (groupName: string, message?: string) => {
    setAlertData({ groupName, message });
    setAlertVisible(true);
  };

  const hideBlockAlert = () => {
    setAlertVisible(false);
    setAlertData({});
  };

  return {
    alertVisible,
    alertData,
    showBlockAlert,
    hideBlockAlert,
    BlockedUserAlert: (
      <BlockedUserAlert
        visible={alertVisible}
        onClose={hideBlockAlert}
        groupName={alertData.groupName}
        message={alertData.message}
      />
    ),
  };
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  alertContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    maxWidth: 320,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 16,
  },
  icon: {
    fontSize: 48,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#d32f2f',
    marginBottom: 12,
    textAlign: 'center',
  },
  message: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  button: {
    backgroundColor: '#d32f2f',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
    minWidth: 100,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default BlockedUserAlert;
