import {useState, useEffect} from 'react';
import {useSelector} from 'react-redux';
import permissionService, {
  MobilePermission,
  UserRole,
} from '../services/permissionService';

interface UsePermissionsReturn {
  hasPermission: (permission: MobilePermission) => boolean;
  canAddPost: boolean;
  canCommentOnPost: boolean;
  canLikePost: boolean;
  canEnterChatForum: boolean;
  canAddExperience: boolean;
  canAddSkills: boolean;
  getUserRole: () => UserRole | null;
  isPermissionsLoaded: boolean;
}

export const usePermissions = (): UsePermissionsReturn => {
  const [isPermissionsLoaded, setIsPermissionsLoaded] = useState(false);
  const isAuthenticated = useSelector(
    (state: any) => state.auth.isAuthenticated,
  );
  const user = useSelector((state: any) => state.auth.userDetails);

  useEffect(() => {
    const checkPermissionsStatus = () => {
      const isLoaded = permissionService.isPermissionsInitialized();
      setIsPermissionsLoaded(isLoaded);
    };

    // Check immediately
    checkPermissionsStatus();

    // If user is authenticated but permissions aren't loaded, try to initialize
    if (
      isAuthenticated &&
      user &&
      !permissionService.isPermissionsInitialized()
    ) {
      console.log(
        'User authenticated but permissions not loaded, initializing...',
      );
      permissionService.initializePermissions(user).catch(error => {
        console.error('Failed to initialize permissions:', error);
      });
    }

    // Check permissions status every 2 seconds
    const interval = setInterval(checkPermissionsStatus, 2000);

    return () => clearInterval(interval);
  }, [isAuthenticated, user]);

  const hasPermission = (permission: MobilePermission): boolean => {
    return permissionService.hasPermission(permission);
  };

  const getUserRole = (): UserRole | null => {
    return permissionService.getUserRole();
  };

  return {
    hasPermission,
    canAddPost: hasPermission('ADD_POST'),
    canCommentOnPost: hasPermission('COMMENT_ON_POST'),
    canLikePost: hasPermission('LIKE_POST'),
    canEnterChatForum: hasPermission('ENTER_CHAT_FORUM'),
    canAddExperience: hasPermission('ADD_EXPERIENCE'),
    canAddSkills: hasPermission('ADD_SKILLS'),
    getUserRole,
    isPermissionsLoaded,
  };
};

// Individual permission hooks for convenience
export const useCanAddPost = (): boolean => {
  const {canAddPost} = usePermissions();
  return canAddPost;
};

export const useCanCommentOnPost = (): boolean => {
  const {canCommentOnPost} = usePermissions();
  return canCommentOnPost;
};

export const useCanAddExperience = (): boolean => {
  const {canAddExperience} = usePermissions();
  return canAddExperience;
};

export const useCanAddSkills = (): boolean => {
  const {canAddSkills} = usePermissions();
  return canAddSkills;
};

export const useCanLikePost = (): boolean => {
  const {canLikePost} = usePermissions();
  return canLikePost;
};

export const useCanEnterChatForum = (): boolean => {
  const {canEnterChatForum} = usePermissions();
  return canEnterChatForum;
};
