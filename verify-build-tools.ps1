# Visual Studio Build Tools Verification Script
# Run this after installing VS Build Tools and restarting your computer

Write-Host "=== Visual Studio Build Tools Verification ===" -ForegroundColor Green
Write-Host ""

# Check if Visual Studio is installed
Write-Host "1. Checking Visual Studio installation..." -ForegroundColor Yellow
$vsPath = "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools"
if (Test-Path $vsPath) {
    Write-Host "✅ Visual Studio Build Tools 2022 found at: $vsPath" -ForegroundColor Green
} else {
    Write-Host "❌ Visual Studio Build Tools 2022 not found" -ForegroundColor Red
    Write-Host "   Expected location: $vsPath" -ForegroundColor Gray
}

Write-Host ""

# Check for C++ compiler
Write-Host "2. Checking C++ compiler (cl.exe)..." -ForegroundColor Yellow
try {
    $clPath = Get-Command cl -ErrorAction Stop
    Write-Host "✅ C++ compiler found at: $($clPath.Source)" -ForegroundColor Green
} catch {
    Write-Host "❌ C++ compiler (cl.exe) not found in PATH" -ForegroundColor Red
    Write-Host "   You may need to run this from a Developer Command Prompt" -ForegroundColor Gray
}

Write-Host ""

# Check for CMake
Write-Host "3. Checking CMake..." -ForegroundColor Yellow
try {
    $cmakePath = Get-Command cmake -ErrorAction Stop
    Write-Host "✅ CMake found at: $($cmakePath.Source)" -ForegroundColor Green
    
    # Get CMake version
    $cmakeVersion = & cmake --version 2>$null | Select-Object -First 1
    Write-Host "   Version: $cmakeVersion" -ForegroundColor Gray
} catch {
    Write-Host "❌ CMake not found in PATH" -ForegroundColor Red
}

Write-Host ""

# Check for MSBuild
Write-Host "4. Checking MSBuild..." -ForegroundColor Yellow
try {
    $msbuildPath = Get-Command msbuild -ErrorAction Stop
    Write-Host "✅ MSBuild found at: $($msbuildPath.Source)" -ForegroundColor Green
} catch {
    Write-Host "❌ MSBuild not found in PATH" -ForegroundColor Red
}

Write-Host ""

# Check Windows SDK
Write-Host "5. Checking Windows SDK..." -ForegroundColor Yellow
$sdkPath = "C:\Program Files (x86)\Windows Kits\10"
if (Test-Path $sdkPath) {
    Write-Host "✅ Windows SDK found at: $sdkPath" -ForegroundColor Green
    
    # List available SDK versions
    $sdkVersions = Get-ChildItem "$sdkPath\bin" -Directory | Sort-Object Name -Descending | Select-Object -First 3
    Write-Host "   Available versions:" -ForegroundColor Gray
    foreach ($version in $sdkVersions) {
        Write-Host "   - $($version.Name)" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ Windows SDK not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Green
Write-Host ""

# Provide next steps
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. If all checks passed, try building your React Native app:" -ForegroundColor White
Write-Host "   cd android && .\gradlew clean && cd .. && npx react-native run-android" -ForegroundColor Gray
Write-Host ""
Write-Host "2. If C++ compiler not found, try running from Developer Command Prompt:" -ForegroundColor White
Write-Host "   - Search for 'Developer Command Prompt for VS 2022' in Start Menu" -ForegroundColor Gray
Write-Host "   - Run your React Native build commands from there" -ForegroundColor Gray
Write-Host ""
Write-Host "3. If issues persist, you may need to add VS tools to your PATH manually" -ForegroundColor White
