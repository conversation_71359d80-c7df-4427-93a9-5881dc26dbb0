import AsyncStorage from '@react-native-async-storage/async-storage';
import apiService from './apiService';

export type MobilePermission =
  | 'ADD_POST'
  | 'COMMENT_ON_POST'
  | 'LIKE_POST'
  | 'ENTER_CHAT_FORUM'
  | 'ADD_EXPERIENCE'
  | 'ADD_SKILLS';
export type UserRole = 'ADMIN' | 'HR' | 'USER' | 'GUEST';

class PermissionService {
  private permissions: MobilePermission[] = [];
  private userRole: UserRole | null = null;
  private isInitialized = false;

  // Initialize permissions when user logs in
  async initializePermissions(userData: any): Promise<void> {
    try {
      const role = userData.role || 'USER';
      this.userRole = role;

      console.log('Initializing permissions for role:', userData);

      // Fetch permissions from backend
      const responseData = await apiService.getJson(
        `permissions/roles/${role}`,
      );
      console.log('responseData:===========>', responseData);

      if (
        responseData &&
        responseData.success &&
        Array.isArray(responseData.data)
      ) {
        // Filter only the permissions we care about
        const allPermissions = responseData.data;
        this.permissions = allPermissions.filter((perm: string) =>
          [
            'ADD_POST',
            'COMMENT_ON_POST',
            'LIKE_POST',
            'ENTER_CHAT_FORUM',
            'ADD_EXPERIENCE',
            'ADD_SKILLS',
          ].includes(perm),
        ) as MobilePermission[];

        console.log(
          `✅ Loaded ${this.permissions.length} permissions for ${role}:`,
          this.permissions,
        );
      } else {
        console.error('Invalid API response, using fallback permissions');
        this.setFallbackPermissions(role);
      }

      // Cache permissions
      await AsyncStorage.setItem(
        'userPermissions',
        JSON.stringify(this.permissions),
      );
      await AsyncStorage.setItem('userRole', role);

      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize permissions:', error);
      // Set fallback permissions based on role
      if (this.userRole) {
        this.setFallbackPermissions(this.userRole);
      }
    }
  }

  // Set fallback permissions when API fails
  private setFallbackPermissions(role: UserRole): void {
    console.log('Setting fallback permissions for role:', role);

    switch (role) {
      case 'GUEST':
        this.permissions = []; // No permissions for guests
        break;
      case 'USER':
        this.permissions = [
          'ADD_POST',
          'COMMENT_ON_POST',
          'LIKE_POST',
          'ENTER_CHAT_FORUM',
          'ADD_EXPERIENCE',
          'ADD_SKILLS',
        ];
        break;
      case 'HR':
      case 'ADMIN':
        this.permissions = [
          'ADD_POST',
          'COMMENT_ON_POST',
          'LIKE_POST',
          'ENTER_CHAT_FORUM',
          'ADD_EXPERIENCE',
          'ADD_SKILLS',
        ];
        break;
      default:
        this.permissions = [];
    }

    this.isInitialized = true;
    console.log('Fallback permissions set:', this.permissions);
  }

  // Load permissions from cache on app startup
  async loadFromCache(): Promise<boolean> {
    try {
      const cachedPermissions = await AsyncStorage.getItem('userPermissions');
      const cachedRole = await AsyncStorage.getItem('userRole');

      if (cachedPermissions && cachedRole) {
        this.permissions = JSON.parse(cachedPermissions);
        this.userRole = cachedRole as UserRole;
        this.isInitialized = true;

        console.log('Loaded permissions from cache:', this.permissions);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to load permissions from cache:', error);
      return false;
    }
  }

  // Check if user has a specific permission
  hasPermission(permission: MobilePermission): boolean {
    if (!this.isInitialized) {
      console.warn(
        'Permissions not initialized, denying access to:',
        permission,
      );
      return false;
    }

    const hasAccess = this.permissions.includes(permission);
    if (!hasAccess) {
      console.log(`Permission denied: ${permission} (role: ${this.userRole})`);
    }
    return hasAccess;
  }

  // Get user role
  getUserRole(): UserRole | null {
    return this.userRole;
  }

  // Get all user permissions
  getUserPermissions(): MobilePermission[] {
    return [...this.permissions];
  }

  // Check if permissions are initialized
  isPermissionsInitialized(): boolean {
    return this.isInitialized;
  }

  // Clear permissions on logout
  async clearPermissions(): Promise<void> {
    this.permissions = [];
    this.userRole = null;
    this.isInitialized = false;

    try {
      await AsyncStorage.removeItem('userPermissions');
      await AsyncStorage.removeItem('userRole');
      console.log('Permissions cleared');
    } catch (error) {
      console.error('Failed to clear permissions:', error);
    }
  }

  // Refresh permissions from server
  async refreshPermissions(): Promise<void> {
    if (!this.userRole) {
      throw new Error('No user role available to refresh permissions');
    }

    // Re-initialize with current role
    await this.initializePermissions({role: this.userRole});
  }
}

// Create singleton instance
const permissionService = new PermissionService();

// Helper functions for the mobile permissions
export const canAddPost = (): boolean =>
  permissionService.hasPermission('ADD_POST');
export const canCommentOnPost = (): boolean =>
  permissionService.hasPermission('COMMENT_ON_POST');
export const canLikePost = (): boolean =>
  permissionService.hasPermission('LIKE_POST');
export const canEnterChatForum = (): boolean =>
  permissionService.hasPermission('ENTER_CHAT_FORUM');
export const canAddExperience = (): boolean =>
  permissionService.hasPermission('ADD_EXPERIENCE');
export const canAddSkills = (): boolean =>
  permissionService.hasPermission('ADD_SKILLS');

export default permissionService;
