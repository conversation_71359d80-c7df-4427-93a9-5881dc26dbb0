import React, {createContext, useContext, useState, useEffect} from 'react';
import {useSelector} from 'react-redux';
import {socket} from '../services/socket';
import NotificationToast from '../components/NotificationToast';

interface NotificationData {
  id: string;
  message: string;
  type: 'like' | 'comment' | 'new_post';
  postId?: number;
  timestamp: Date;
}

interface NotificationContextType {
  notifications: NotificationData[];
  showNotification: (
    notification: Omit<NotificationData, 'id' | 'timestamp'>,
  ) => void;
  clearNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      'useNotification must be used within a NotificationProvider',
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [currentNotification, setCurrentNotification] =
    useState<NotificationData | null>(null);
  const loginUserId = useSelector((state: any) => state.auth.userId);

  useEffect(() => {
    if (!loginUserId) return;

    // Join user room to receive notifications
    socket.emit('joinUserRoom', loginUserId);

    // Listen for new likes
    const handleNewLike = (data: any) => {
      const notification: NotificationData = {
        id: `like_${data.postId}_${Date.now()}`,
        message: `${data.likerName} liked your post`,
        type: 'like',
        postId: data.postId,
        timestamp: new Date(data.timestamp),
      };

      setNotifications(prev => [notification, ...prev]);
      setCurrentNotification(notification);
    };

    // Listen for new comments
    const handleNewComment = (data: any) => {
      const notification: NotificationData = {
        id: `comment_${data.postId}_${Date.now()}`,
        message: `${data.commenterName} commented on your post`,
        type: 'comment',
        postId: data.postId,
        timestamp: new Date(data.timestamp),
      };

      setNotifications(prev => [notification, ...prev]);
      setCurrentNotification(notification);
    };

    // Listen for new posts
    const handleNewPost = (data: any) => {
      const notification: NotificationData = {
        id: `post_${data.postId}_${Date.now()}`,
        message: `${data.authorName} shared a new post`,
        type: 'new_post',
        postId: data.postId,
        timestamp: new Date(data.timestamp),
      };

      setNotifications(prev => [notification, ...prev]);
      setCurrentNotification(notification);
    };

    socket.on('newLike', handleNewLike);
    socket.on('newComment', handleNewComment);
    socket.on('newPost', handleNewPost);

    return () => {
      socket.off('newLike', handleNewLike);
      socket.off('newComment', handleNewComment);
      socket.off('newPost', handleNewPost);
    };
  }, [loginUserId]);

  const showNotification = (
    notification: Omit<NotificationData, 'id' | 'timestamp'>,
  ) => {
    const newNotification: NotificationData = {
      ...notification,
      id: `${notification.type}_${Date.now()}`,
      timestamp: new Date(),
    };

    setNotifications(prev => [newNotification, ...prev]);
    setCurrentNotification(newNotification);
  };

  const clearNotifications = () => {
    setNotifications([]);
  };

  const handleNotificationPress = () => {
    // Navigate to home or specific post
    setCurrentNotification(null);
  };

  const handleNotificationHide = () => {
    setCurrentNotification(null);
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        showNotification,
        clearNotifications,
      }}>
      {children}

      {/* Notification Toast */}
      <NotificationToast
        visible={currentNotification !== null}
        message={currentNotification?.message || ''}
        type={currentNotification?.type || 'like'}
        onPress={handleNotificationPress}
        onHide={handleNotificationHide}
      />
    </NotificationContext.Provider>
  );
};
