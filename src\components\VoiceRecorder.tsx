import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface VoiceRecorderProps {
  onRecordingComplete: (audioPath: string, duration: number) => void;
  onCancel: () => void;
  isRecording: boolean;
  setIsRecording: (recording: boolean) => void;
  isSending?: boolean;
}

const VoiceRecorder: React.FC<VoiceRecorderProps> = ({
  onRecordingComplete,
  onCancel,
  isRecording,
  setIsRecording,
  isSending = false,
}) => {
  const [recordTime, setRecordTime] = useState('00:00');
  const [currentPositionSec, setCurrentPositionSec] = useState(0);
  const audioRecorderPlayer = useRef(new AudioRecorderPlayer()).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const hasStartedRecording = useRef(false);

  useEffect(() => {
    if (isRecording) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [isRecording, pulseAnim]);

  // Auto-start recording when component mounts with isRecording=true
  useEffect(() => {
    if (isRecording && !hasStartedRecording.current) {
      // Only start if we're supposed to be recording but haven't started yet
      hasStartedRecording.current = true;
      // Start recording automatically
      const autoStart = async () => {
        const hasPermission = await requestMicrophonePermission();
        if (hasPermission) {
          try {
            // Stop any existing recording first
            try {
              await audioRecorderPlayer.stopRecorder();
              audioRecorderPlayer.removeRecordBackListener();
            } catch (stopError) {
              console.log('No active recording to stop');
            }

            console.log('Auto-starting recording...');
            const result = await audioRecorderPlayer.startRecorder();

            audioRecorderPlayer.addRecordBackListener(e => {
              setRecordTime(
                audioRecorderPlayer.mmssss(Math.floor(e.currentPosition)),
              );
              setCurrentPositionSec(e.currentPosition);
            });

            console.log('Auto-recording started successfully:', result);
          } catch (error) {
            console.error('Auto-start recording error:', error);
            setIsRecording(false);
            onCancel();
          }
        } else {
          setIsRecording(false);
          onCancel();
        }
      };
      autoStart();
    } else if (!isRecording) {
      hasStartedRecording.current = false;
    }
  }, [isRecording, audioRecorderPlayer, onCancel, setIsRecording]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Cleanup recording if component unmounts while recording
      if (isRecording) {
        audioRecorderPlayer.stopRecorder().catch(console.error);
        audioRecorderPlayer.removeRecordBackListener();
      }
    };
  }, [isRecording, audioRecorderPlayer]);

  const requestMicrophonePermission = async () => {
    try {
      if (Platform.OS === 'android') {
        // Request only audio permission for Android
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Microphone Permission',
            message:
              'This app needs access to your microphone to record voice messages.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          },
        );

        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } else {
        // For iOS, permissions are handled automatically by the system
        return true;
      }
    } catch (error) {
      console.error('Permission request error:', error);
      return false;
    }
  };

  const startRecording = async () => {
    const hasPermission = await requestMicrophonePermission();

    if (!hasPermission) {
      Alert.alert(
        'Permission Required',
        'Microphone permission is required to record voice messages.',
        [
          {text: 'Cancel', style: 'cancel'},
          {
            text: 'Settings',
            onPress: () => {
              /* Open settings */
            },
          },
        ],
      );
      return;
    }

    try {
      // Stop any existing recording first
      try {
        await audioRecorderPlayer.stopRecorder();
        audioRecorderPlayer.removeRecordBackListener();
      } catch (stopError) {
        // Ignore errors if no recording was active
        console.log('No active recording to stop');
      }

      // Use AudioRecorderPlayer's default path (no path parameter)
      // This will use the app's writable directory automatically
      console.log('Starting recording with default path...');

      // Start recording without specifying path - uses internal writable directory
      const result = await audioRecorderPlayer.startRecorder();

      audioRecorderPlayer.addRecordBackListener(e => {
        setRecordTime(
          audioRecorderPlayer.mmssss(Math.floor(e.currentPosition)),
        );
        setCurrentPositionSec(e.currentPosition);
      });

      setIsRecording(true);
      console.log('Recording started successfully:', result);
    } catch (error) {
      console.error('Start recording error:', error);

      // Provide more specific error messages
      let errorMessage = 'Failed to start recording';
      const errorStr = error instanceof Error ? error.message : String(error);

      if (errorStr.includes('EROFS')) {
        errorMessage =
          'Storage error: Unable to write to file system. Please try again.';
      } else if (errorStr.includes('EPERM')) {
        errorMessage =
          'Permission error: Unable to access storage. Please check app permissions.';
      } else if (errorStr.includes('ENOENT')) {
        errorMessage =
          'File system error: Directory not found. Please restart the app.';
      }

      Alert.alert('Recording Error', errorMessage);
      setIsRecording(false);
    }
  };

  const stopRecording = async () => {
    try {
      // Check if we're actually recording
      if (!isRecording) {
        console.log('Stop recording called but not currently recording');
        return;
      }

      const result = await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setIsRecording(false);

      const durationInSeconds = Math.floor(currentPositionSec / 1000);

      if (durationInSeconds < 1) {
        Alert.alert(
          'Recording too short',
          'Please record for at least 1 second',
        );
        setRecordTime('00:00');
        setCurrentPositionSec(0);
        return;
      }

      onRecordingComplete(result, durationInSeconds);
      setRecordTime('00:00');
      setCurrentPositionSec(0);

      console.log('Recording stopped successfully:', result);
    } catch (error) {
      console.error('Stop recording error:', error);
      setIsRecording(false);
      setRecordTime('00:00');
      setCurrentPositionSec(0);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const cancelRecording = async () => {
    try {
      // Check if we're actually recording
      if (!isRecording) {
        console.log('Cancel recording called but not currently recording');
        onCancel();
        return;
      }

      await audioRecorderPlayer.stopRecorder();
      audioRecorderPlayer.removeRecordBackListener();
      setIsRecording(false);
      setRecordTime('00:00');
      setCurrentPositionSec(0);
      onCancel();
      console.log('Recording cancelled successfully');
    } catch (error) {
      console.error('Cancel recording error:', error);
      // Still reset state even if stop fails
      setIsRecording(false);
      setRecordTime('00:00');
      setCurrentPositionSec(0);
      onCancel();
    }
  };

  if (!isRecording) {
    return (
      <TouchableOpacity
        style={styles.recordButton}
        onPress={startRecording}
        activeOpacity={0.7}>
        <Ionicons name="mic" size={24} color="#007bff" />
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.recordingContainer}>
      <TouchableOpacity
        style={styles.cancelButton}
        onPress={cancelRecording}
        activeOpacity={0.7}>
        <Ionicons name="close" size={20} color="#ff4444" />
      </TouchableOpacity>

      <View style={styles.recordingInfo}>
        <Animated.View
          style={[
            styles.recordingIndicator,
            {transform: [{scale: pulseAnim}]},
          ]}>
          <View style={styles.recordingDot} />
        </Animated.View>
        <Text style={styles.recordingTime}>{recordTime}</Text>
      </View>

      <TouchableOpacity
        style={[styles.stopButton, isSending && styles.sendingButton]}
        onPress={stopRecording}
        disabled={isSending}
        activeOpacity={0.7}>
        <Ionicons
          name={isSending ? 'hourglass' : 'send'}
          size={20}
          color={isSending ? '#999' : '#007bff'}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  recordButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f2f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  recordingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 25,
    marginLeft: 8,
    flex: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  cancelButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#ffebee',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  recordingIndicator: {
    marginRight: 8,
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#ff4444',
  },
  recordingTime: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  stopButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#e3f2fd',
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendingButton: {
    backgroundColor: '#f5f5f5',
    opacity: 0.7,
  },
});

export default VoiceRecorder;
