import AsyncStorage from '@react-native-async-storage/async-storage';
import apiClient from '../services/axios';

export const testTokenRefresh = async () => {
  try {
    console.log('=== TESTING TOKEN REFRESH ===');
    
    // Check if we have tokens
    const accessToken = await AsyncStorage.getItem('accessToken');
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    
    if (!accessToken || !refreshToken) {
      console.log('No tokens found. Please login first.');
      return;
    }
    
    console.log('Tokens found. Testing refresh...');
    
    // Try to make a request that requires authentication
    // This should trigger the refresh token flow if the access token is expired
    try {
      const response = await apiClient.get('/users/user_details');
      console.log('API call successful:', response.status);
    } catch (error: any) {
      console.log('API call failed:', error.response?.status, error.message);
    }
    
    // Check if tokens were updated
    const newAccessToken = await AsyncStorage.getItem('accessToken');
    const newRefreshToken = await AsyncStorage.getItem('refreshToken');
    
    console.log('Access token changed:', accessToken !== newAccessToken);
    console.log('Refresh token changed:', refreshToken !== newRefreshToken);
    
    console.log('=== END TOKEN REFRESH TEST ===');
  } catch (error) {
    console.error('Error testing token refresh:', error);
  }
};

export const testRefreshTokenDirectly = async () => {
  try {
    console.log('=== TESTING REFRESH TOKEN DIRECTLY ===');
    
    const refreshToken = await AsyncStorage.getItem('refreshToken');
    
    if (!refreshToken) {
      console.log('No refresh token found');
      return;
    }
    
    console.log('Making direct refresh token request...');
    
    const response = await apiClient.post('/auth/refresh-token', {
      refreshToken: refreshToken,
    });
    
    console.log('Refresh response:', response.status, response.data);
    
    if (response.data.accessToken) {
      await AsyncStorage.setItem('accessToken', response.data.accessToken);
      console.log('New access token saved');
    }
    
    console.log('=== END DIRECT REFRESH TEST ===');
  } catch (error: any) {
    console.error('Direct refresh failed:', error.response?.data || error.message);
  }
};
