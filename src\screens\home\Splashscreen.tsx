import React, {useEffect} from 'react';
import {View, StyleSheet, Animated, Image, Text} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {RootStackParamList} from '../../types';
import normalize from '../../types/utiles';

type Props = NativeStackScreenProps<RootStackParamList, 'Splashscreen'>;

const SplashScreen: React.FC<Props> = ({navigation}) => {
  const scaleAnim = new Animated.Value(0.5);
  const opacityAnim = new Animated.Value(0);

  useEffect(() => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ]).start(() => {
      navigation.replace('Welcome');
    });
  }, [navigation, scaleAnim, opacityAnim]);

  return (
    <LinearGradient colors={['#1E3C72', '#2A5298']} style={styles.container}>
      <Animated.View
        style={[
          styles.logoContainer,
          {transform: [{scale: scaleAnim}], opacity: opacityAnim},
        ]}>
        <Image
          source={{
            uri: 'https://res.cloudinary.com/dnkavsz6z/image/upload/v1737005088/SGIC_r9fqvo.png',
          }}
          style={styles.logoImage}
          resizeMode="contain"
        />
        {/* <Animated.Text style={[styles.appName, { opacity: opacityAnim }]}>
                    Samuel Gnanam IT Centre
                </Animated.Text> */}
      </Animated.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
  },
  logoImage: {
    width: normalize(200),
    height: normalize(200),
  },
  appName: {
    marginTop: normalize(20),
    fontSize: normalize(24),
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
});

export default SplashScreen;
