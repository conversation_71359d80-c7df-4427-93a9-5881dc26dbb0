import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from './PermissionGuard';

/**
 * Test component to verify ADD_EXPERIENCE and ADD_SKILLS permissions are working
 * You can temporarily add this to any screen to test the permissions
 */
const PermissionTestComponent: React.FC = () => {
  const { 
    hasPermission, 
    canAddExperience, 
    canAddSkills, 
    getUserRole, 
    isPermissionsLoaded 
  } = usePermissions();

  if (!isPermissionsLoaded) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading permissions...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🔐 Permission Test</Text>
      
      {/* User Info */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>User Info:</Text>
        <Text style={styles.infoText}>Role: {getUserRole()}</Text>
        <Text style={styles.infoText}>Permissions Loaded: {isPermissionsLoaded ? '✅' : '❌'}</Text>
      </View>

      {/* Permission Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Permission Status:</Text>
        <Text style={[styles.permissionText, canAddExperience ? styles.allowed : styles.denied]}>
          ADD_EXPERIENCE: {canAddExperience ? '✅ Allowed' : '❌ Denied'}
        </Text>
        <Text style={[styles.permissionText, canAddSkills ? styles.allowed : styles.denied]}>
          ADD_SKILLS: {canAddSkills ? '✅ Allowed' : '❌ Denied'}
        </Text>
      </View>

      {/* Test Buttons with PermissionGuard */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Protected Buttons:</Text>
        
        <PermissionGuard permission="ADD_EXPERIENCE">
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Add Experience (Protected)</Text>
          </TouchableOpacity>
        </PermissionGuard>

        <PermissionGuard permission="ADD_SKILLS">
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Add Skills (Protected)</Text>
          </TouchableOpacity>
        </PermissionGuard>
      </View>

      {/* Test Buttons with Conditional Rendering */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Conditional Buttons:</Text>
        
        {canAddExperience && (
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Add Experience (Conditional)</Text>
          </TouchableOpacity>
        )}

        {canAddSkills && (
          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Add Skills (Conditional)</Text>
          </TouchableOpacity>
        )}

        {!canAddExperience && !canAddSkills && (
          <View style={styles.noPermissionContainer}>
            <Text style={styles.noPermissionText}>
              No permissions to add experience or skills
            </Text>
          </View>
        )}
      </View>

      {/* Direct Permission Checks */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Direct Permission Checks:</Text>
        <Text style={styles.infoText}>
          hasPermission('ADD_EXPERIENCE'): {hasPermission('ADD_EXPERIENCE') ? '✅' : '❌'}
        </Text>
        <Text style={styles.infoText}>
          hasPermission('ADD_SKILLS'): {hasPermission('ADD_SKILLS') ? '✅' : '❌'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f8f9fa',
    margin: 10,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#007bff',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#007bff',
  },
  section: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#495057',
  },
  infoText: {
    fontSize: 14,
    color: '#6c757d',
    marginVertical: 2,
  },
  permissionText: {
    fontSize: 14,
    fontWeight: '600',
    marginVertical: 2,
  },
  allowed: {
    color: '#28a745',
  },
  denied: {
    color: '#dc3545',
  },
  button: {
    backgroundColor: '#007bff',
    padding: 12,
    borderRadius: 6,
    marginVertical: 4,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  noPermissionContainer: {
    backgroundColor: '#fff3cd',
    padding: 10,
    borderRadius: 6,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  noPermissionText: {
    color: '#856404',
    fontSize: 14,
    textAlign: 'center',
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#6c757d',
  },
});

export default PermissionTestComponent;
