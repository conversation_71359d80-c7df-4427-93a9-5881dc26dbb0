import React, { useState } from 'react';
import { View, Text, Image, FlatList, TouchableOpacity, TextInput, StyleSheet, Button } from 'react-native';

// Sample data for posts and comments
const posts = [
  {
    id: '1',
    user: '<PERSON>',
    time: '2 hours ago',
    content: 'This is a post about React Native!',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    likes: 10,
    comments: [
      { id: '1', user: '<PERSON>', comment: 'Great post!' },
      { id: '2', user: '<PERSON>', comment: 'I agree, React Native is amazing!' },
    ],
  },
  {
    id: '2',
    user: '<PERSON>',
    time: '3 hours ago',
    content: 'Loving the React Native updates!',
    avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
    likes: 5,
    comments: [
      { id: '1', user: '<PERSON>', comment: 'So exciting!' },
    ],
  },
];

const SettingsScreen = () => {
  const [newPost, setNewPost] = useState('');
  const [postsData, setPostsData] = useState(posts);

  const handleLike = (postId: string) => {
    setPostsData(prevPosts =>
      prevPosts.map(post => post.id === postId
        ? { ...post, likes: post.likes + 1 }
        : post
      )
    );
  };

  const handleAddPost = () => {
    const newPostObj = {
      id: `${postsData.length + 1}`,
      user: 'New User',
      time: 'Just now',
      content: newPost,
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      likes: 0,
      comments: [],
    };

    setPostsData([newPostObj, ...postsData]);
    setNewPost('');
  };

  const renderItem = ({ item }: { item: typeof posts[0] }) => (
    <View style={styles.postContainer}>
      <View style={styles.postHeader}>
        <Image source={{ uri: item.avatar }} style={styles.avatar} />
        <View style={styles.postDetails}>
          <Text style={styles.userName}>{item.user}</Text>
          <Text style={styles.postTime}>{item.time}</Text>
        </View>
      </View>
      <Text style={styles.postContent}>{item.content}</Text>

      <TouchableOpacity onPress={() => handleLike(item.id)} style={styles.likeButton}>
        <Text style={styles.likeText}>👍 {item.likes} Likes</Text>
      </TouchableOpacity>

      <FlatList
        data={item.comments}
        keyExtractor={(comment) => comment.id}
        renderItem={({ item: comment }) => (
          <View style={styles.comment}>
            <Text style={styles.commentUser}>{comment.user}: </Text>
            <Text style={styles.commentText}>{comment.comment}</Text>
          </View>
        )}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      {/* New Post Section */}
      <TextInput
        style={styles.input}
        placeholder="Write something..."
        value={newPost}
        onChangeText={setNewPost}
        multiline
      />
      <Button title="Post" onPress={handleAddPost} disabled={!newPost} />

      <FlatList
        data={postsData}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        style={styles.postList}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f5f5f5',
  },
  input: {
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    height: 100,
    textAlignVertical: 'top',
  },
  postList: {
    marginTop: 10,
  },
  postContainer: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
  },
  postHeader: {
    flexDirection: 'row',
    marginBottom: 10,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 10,
  },
  postDetails: {
    justifyContent: 'center',
  },
  userName: {
    fontWeight: 'bold',
  },
  postTime: {
    color: '#777',
  },
  postContent: {
    fontSize: 16,
    color: '#333',
  },
  likeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  likeText: {
    color: '#0073b1',
    fontWeight: 'bold',
  },
  comment: {
    marginTop: 5,
    flexDirection: 'row',
  },
  commentUser: {
    fontWeight: 'bold',
  },
  commentText: {
    color: '#555',
  },
});

export default SettingsScreen;
