/**
 * Learn more about using TypeScript with React Navigation:
 * https://reactnavigation.org/docs/typescript/
 */

import {NativeStackScreenProps} from '@react-navigation/native-stack';

declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

export type RootStackParamList = {
  Splashscreen: undefined;
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
  PostLogin: undefined;
  AddPost: undefined;
  UpdatePost: undefined;
  Profile: undefined;
  ViewUserProfile: undefined;
  AppShare: undefined;
  chat: undefined;
  GroupMembers: {
    groupId: string;
    groupName: string;
    groupImage?: string;
  };
  NoticeDetail: {
    noticeId: number;
    notice?: any;
  };
};

export type PostLoginTabsParamList = {
  Home: undefined;
  Profile: undefined;
  Forum: undefined;
  Notice: undefined;
  Main: undefined;
};

export type RootStackScreenProps<Screen extends keyof RootStackParamList> =
  NativeStackScreenProps<RootStackParamList, Screen>;
