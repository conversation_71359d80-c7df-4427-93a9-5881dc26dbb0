import {createNavigationContainerRef} from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();
let pendingNavigation: {name: string; params: any} | null = null;
export function navigate(name: string, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  } else {
    console.log('Navigation not ready, storing intent...');
    pendingNavigation = {name, params};
  }
}

export function handlePendingNavigation() {
  if (pendingNavigation && navigationRef.isReady()) {
    console.log('Handling pending navigation:', pendingNavigation);
    navigate(pendingNavigation.name, pendingNavigation.params);
    pendingNavigation = null;
  }
}
